<template>
	<view class="income-management-page">

		<!-- 日期选择器 -->
		<view class="date-selector">
			<view class="date-title">
				<text class="date-label">时间范围:</text>
			</view>
			<view class="date-controls">
				<picker mode="date" :value="startDate" @change="onStartDateChange">
					<view class="date-input">
						<text>{{ startDate || '开始日期' }}</text>
					</view>
				</picker>
				<text class="date-separator">~</text>
				<picker mode="date" :value="endDate" @change="onEndDateChange">
					<view class="date-input">
						<text>{{ endDate || '结束日期' }}</text>
					</view>
				</picker>
				<!-- 回到本月按钮 -->
				<view class="reset-button" v-if="!isCurrentMonth" @click="resetToCurrentMonth">
					<text class="reset-text">本月</text>
				</view>
			</view>
		</view>

		<!-- 数据卡片 -->
		<view class="cards-container">
			<view class="card">
				<view class="card-header">
					<text class="card-icon">🌱</text>
					<text class="card-title">销售数据</text>
				</view>
				<view class="data-row">
					<text class="data-label">记录数:</text>
					<text class="data-value">{{ summary.totalProduction }}</text>
				</view>
				<view class="data-row">
					<text class="data-label">总产量 (斤):</text>
					<text class="data-value">{{ summary.totalProduction.toFixed(2) }}</text>
				</view>
				<view class="data-row">
					<text class="data-label">平均单价:</text>
					<text class="data-value">¥{{ summary.avgPrice.toFixed(2) }}</text>
				</view>
				<view class="data-row">
					<text class="data-label">总收入:</text>
					<text class="data-value">¥{{ summary.totalIncome.toFixed(2) }}</text>
				</view>
			</view>

			<view class="card">
				<view class="card-header">
					<text class="card-icon">💰</text>
					<text class="card-title">支出数据</text>
				</view>
				<view class="data-row">
					<text class="data-label">采茶支出:</text>
					<text class="data-value">¥{{ summary.pickingCost.toFixed(2) }}</text>
				</view>
				<view class="data-row">
					<text class="data-label">时工支出:</text>
					<text class="data-value">¥{{ summary.laborCost.toFixed(2) }}</text>
				</view>
				<view class="data-row">
					<text class="data-label">总支出:</text>
					<text class="data-value">¥{{ summary.totalCost.toFixed(2) }}</text>
				</view>
			</view>

			<view class="card profit-card">
				<view class="card-header">
					<text class="card-icon">🪙</text>
					<text class="card-title">利润数据</text>
				</view>
				<view class="data-row">
					<text class="data-label">毛利润:</text>
					<text class="data-value profit-value" :class="summary.profit >= 0 ? 'positive' : 'negative'">
						¥{{ summary.profit.toFixed(2) }}
					</text>
				</view>
				<view class="data-row">
					<text class="data-label">利润率:</text>
					<text class="data-value" :class="summary.profitMargin >= 0 ? 'positive' : 'negative'">
						{{ (summary.profitMargin * 100).toFixed(1) }}%
					</text>
				</view>
			</view>
		</view>


		<!-- 收入详情管理区域 -->
		<view class="income-details-management-section">
			<view class="income-section-header">
				<view class="section-title-group">
					<text class="section-icon">📋</text>
					<text class="section-title-text">收入详情</text>
				</view>
				<view class="records-count-badge" v-if="filteredData.length > 0">
					<text class="count-display-text">共 {{ filteredData.length }} 条记录</text>
				</view>
			</view>

			<!-- 数据加载状态显示 -->
			<view v-if="loading" class="data-loading-state">
				<view class="loading-status-card">
					<text class="loading-status-icon">⏳</text>
					<text class="loading-status-title">数据加载中</text>
					<text class="loading-status-description">正在获取收入记录...</text>
				</view>
			</view>

			<!-- 空数据状态显示 -->
			<view v-else-if="filteredData.length === 0" class="data-empty-state">
				<view class="empty-status-card">
					<text class="empty-status-icon">📋</text>
					<text class="empty-status-title">暂无收入数据</text>
					<text class="empty-status-description">当前时间段内没有收入记录</text>
				</view>
			</view>

			<!-- 收入记录列表容器 -->
			<view v-else class="income-records-container">
				<view class="income-record-item" :data-profit-status="getIncomeRecordProfitStatus(item)"
					v-for="(item, index) in filteredData" :key="index" @click="goToDetail(item)">
					<view class="record-display-card">
						<view class="record-card-header">
							<view class="record-date-display-section">
								<text class="date-display-icon">📅</text>
								<view class="date-display-info">
									<text class="date-display-text">{{ formatDateToShortDisplay(item.date) }}</text>
									<text class="weekday-display-text">{{ formatDateToWeekdayDisplay(item.date) }}</text>
								</view>
							</view>
							<view class="profit-status-indicator-badge" :class="getIncomeRecordProfitStatus(item)">
								<text class="status-indicator-icon">{{ getRecordStatusIcon(item) }}</text>
								<text class="status-indicator-text">{{ getRecordStatusText(item) }}</text>
							</view>
						</view>

						<view class="record-content-area">
							<!-- 基础业务信息展示 -->
							<view class="basic-business-info-summary">
								<view class="business-info-grid">
									<view class="business-info-card">
										<text class="business-info-icon">🍃</text>
										<view class="business-info-details">
											<text class="business-info-label">产量</text>
											<text class="business-info-value">{{ (item.production || 0).toFixed(2) }}斤</text>
										</view>
									</view>
									<view class="business-info-card">
										<text class="business-info-icon">💵</text>
										<view class="business-info-details">
											<text class="business-info-label">售卖单价</text>
											<text class="business-info-value">¥{{ getRecordAveragePriceDisplay(item) }}/斤</text>
										</view>
									</view>
								</view>
								<view class="customer-information-display" v-if="!item.isPlaceholder">
									<text class="customer-information-text" :class="getRecordProfitStyleClass(item)">
										客户: {{ getCompleteCustomerInfo(item) }}
									</text>
								</view>
							</view>

							<!-- 财务数据详情 -->
							<view class="financial-data-details-summary">
								<view class="financial-data-grid">
									<view class="primary-financial-card">
										<text class="primary-financial-icon">💰</text>
										<view class="primary-financial-info">
											<text class="primary-financial-label">总收入</text>
											<text class="primary-financial-value income">¥{{ calculateRecordTotalIncome(item) }}</text>
										</view>
									</view>
									<view class="business-info-card">
										<text class="business-info-icon">💸</text>
										<view class="business-info-details">
											<text class="business-info-label">成本</text>
											<text class="business-info-value">¥{{ calculateItemTotalCost(item).toFixed(2) }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>

						<view class="record-summary-footer">
							<view class="profit-summary-display">
								<text class="profit-summary-label">毛利润</text>
								<text class="profit-summary-value" :class="getRecordProfitStyleClass(item)">¥{{ calculateRecordProfit(item) }}</text>
							</view>
							<view class="detail-navigation-button">
								<text class="navigation-button-text">查看详情</text>
								<text class="navigation-button-icon">→</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import SalesManager from '../../utils/salesManager.js'

export default {
	name: 'IncomeManagementPage',
	data() {
		return {
			startDate: '',
			endDate: '',
			selectedRowId: null, // 用于跟踪当前选中的行
			records: [], // 改为空数组，从销售记录加载数据
			salesManager: null,
			loading: false,
			isLoadingData: false // 防止重复加载数据的标志
		}
	},
	computed: {
		// 过滤当前时间范围内的数据
		filteredData() {
			console.log('🔍 [收入页面] filteredData计算属性被调用')
			console.log('🔍 [收入页面] records数组长度:', this.records?.length || 0)
			console.log('🔍 [收入页面] 日期范围:', this.startDate, '到', this.endDate)

			if (!this.records || this.records.length === 0) {
				console.log('🔍 [收入页面] records为空，返回空数组')
				return []
			}
			if (!this.startDate || !this.endDate) {
				console.log('🔍 [收入页面] 日期范围无效，返回全部records')
				return this.records
			}

			const filtered = this.records.filter(item => {
				if (!item.date) return false
				const itemDate = new Date(item.date)
				const start = new Date(this.startDate)
				const end = new Date(this.endDate)
				const isInRange = itemDate >= start && itemDate <= end
				return isInRange
			})

			console.log('🔍 [收入页面] 过滤后的数据长度:', filtered.length)
			console.log('🔍 [收入页面] 过滤后的数据:', filtered.map(r => ({
				id: r.id,
				date: r.date,
				production: r.production
			})))

			return filtered
		},

		// 判断当前选择的日期范围是否为本月完整范围
		isCurrentMonth() {
			const currentMonthStart = this.getCurrentMonthStart()
			const currentMonthEnd = this.getCurrentMonthEnd()

			// 必须有完整的日期范围才能进行比较
			if (!this.startDate || !this.endDate) {
				return false
			}

			const selectedStart = this.startDate
			const selectedEnd = this.endDate

			// 精确匹配当前月份的完整日期范围
			return selectedStart === currentMonthStart && selectedEnd === currentMonthEnd
		},
		// 计算汇总数据
		summary() {
			const data = this.filteredData
			// 只计算有实际销售数据的记录
			const actualSalesData = data.filter(item => !item.isPlaceholder && item.price !== null)

			// 确保所有计算都使用数字类型
			const totalProduction = data.reduce((sum, item) => {
				const production = parseFloat(item.production) || 0
				return sum + production
			}, 0)

			const totalIncome = actualSalesData.reduce((sum, item) => {
				const price = parseFloat(item.price) || 0
				const production = parseFloat(item.production) || 0
				return sum + (price * production)
			}, 0)

			// 计算总成本（采茶支出 + 时工支出 + 其他支出）
			const totalCost = data.reduce((sum, item) => {
				const teaPickingCost = parseFloat(item.teaPickingCost) || 0
				const laborCost = parseFloat(item.laborCost) || 0
				const otherCost = parseFloat(item.otherCost) || 0
				return sum + teaPickingCost + laborCost + otherCost
			}, 0)

			const profit = totalIncome - totalCost
			const profitMargin = totalIncome > 0 ? (profit / totalIncome) : 0

			const avgPrice = actualSalesData.length > 0 ?
				actualSalesData.reduce((sum, item) => {
					const price = parseFloat(item.price) || 0
					const production = parseFloat(item.production) || 0
					return sum + (price * production)
				}, 0) / actualSalesData.reduce((sum, item) => {
					const production = parseFloat(item.production) || 0
					return sum + production
				}, 0) : 0

			const pickingCost = data.reduce((sum, item) => {
				const cost = parseFloat(item.teaPickingCost) || 0
				return sum + cost
			}, 0)

			// 计算时工支出（从工作记录中获取）
			const laborCost = data.reduce((sum, item) => {
				const cost = parseFloat(item.laborCost) || 0
				return sum + cost
			}, 0)

			return {
				totalProduction,
				avgPrice,
				totalIncome,
				pickingCost,
				laborCost,
				totalCost,
				profit,
				profitMargin
			}
		}
	},
	async onLoad() {
		try {
			// 初始化销售管理器
			this.salesManager = new SalesManager()

			// 页面加载时自动设置为当前月份的完整日期范围
			this.setCurrentMonthDates()

			// 初始化销售记录系统
			await this.initializeSalesSystem()

			// 加载销售记录数据
			await this.loadSalesRecords()

			// 监听详情页的数据更新事件
			console.log('🎧 [收入页面] 注册updateIncomeRecord事件监听器')
			uni.$on('updateIncomeRecord', this.handleRecordUpdate)
			console.log('🎧 [收入页面] 事件监听器注册完成')

		} catch (error) {
			uni.showToast({
				title: '页面加载失败',
				icon: 'error',
				duration: 3000
			})
		}
	},

	onUnload() {
		// 移除事件监听
		uni.$off('updateIncomeRecord', this.handleRecordUpdate)
	},

	async onShow() {
		// 页面显示时重新加载数据（从其他页面返回时）
		try {
			// 重新注册事件监听器（防止页面切换时监听器丢失）
			console.log('🎧 [收入页面] 页面显示，重新注册事件监听器')
			uni.$off('updateIncomeRecord', this.handleRecordUpdate) // 先移除避免重复
			uni.$on('updateIncomeRecord', this.handleRecordUpdate)
			console.log('🎧 [收入页面] 事件监听器重新注册完成')

			// 确保销售管理器已初始化
			if (!this.salesManager) {
				console.log('收入页面显示：销售管理器未初始化，跳过数据刷新')
				return
			}

			// 显示加载状态（短暂显示，避免闪烁）
			this.loading = true

			// 重新加载销售记录数据
			await this.loadSalesRecords()

			console.log('收入页面显示：数据刷新完成')

		} catch (error) {
			console.error('收入页面显示时数据刷新失败:', error)

			// 显示用户友好的错误提示
			uni.showToast({
				title: '数据刷新失败',
				icon: 'none',
				duration: 2000
			})
		} finally {
			// 确保加载状态被清除
			this.loading = false
		}
	},
	methods: {
		/**
		 * 初始化销售记录系统
		 */
		async initializeSalesSystem() {
			try {
				this.loading = true
				// 确保销售记录表存在
				uni.getStorageSync('sales_records') || []
			} catch (error) {
				uni.showToast({
					title: '系统初始化失败',
					icon: 'error'
				})
			} finally {
				this.loading = false
			}
		},

		/**
		 * 加载销售记录数据
		 */
		async loadSalesRecords() {
			// 防止重复调用
			if (this.isLoadingData) {
				console.log('数据正在加载中，跳过重复请求')
				return
			}

			try {
				this.isLoadingData = true
				this.loading = true

				// 确保销售管理器已初始化
				if (!this.salesManager) {
					console.log('销售管理器未初始化，清空记录')
					this.records = []
					return
				}

				// 验证日期范围
				if (!this.startDate || !this.endDate) {
					console.log('日期范围无效，跳过数据加载')
					return
				}

				console.log('📅 [收入页面] 开始加载销售记录数据，日期范围:', this.startDate, '到', this.endDate)

				// 获取日期范围内的收入记录
				const incomeRecords = this.salesManager.getIncomeRecordsByDateRange(this.startDate, this.endDate)
				console.log('📊 [收入页面] 从salesManager获取到的原始记录数:', incomeRecords.length)
				console.log('📊 [收入页面] 原始记录详情:', incomeRecords.map(r => ({
					id: r.id,
					date: r.date,
					production: r.production || r.dailyTotalProduction,
					customer_name: r.customer_name,
					isPlaceholder: r.isPlaceholder,
					isAggregated: r.isAggregated
				})))

				// 转换数据格式以适配现有的UI
				console.log('🔄 [收入页面] 转换前records数组长度:', this.records.length)
				const newRecords = incomeRecords.map(record => {
					// 处理汇总记录的客户显示
					let customerDisplay = ''
					if (record.isPlaceholder) {
						customerDisplay = null
					} else if (record.isAggregated && record.customer_count > 1) {
						customerDisplay = `${record.customer_count}客户`
					} else {
						customerDisplay = record.customer_name || ''
					}

					// 分别计算采茶成本和时工成本
					const { teaPickingCost, laborCost } = this.calculateSeparateCosts(record)
					const otherIncome = parseFloat(record.other_income) || 0
					const otherCost = parseFloat(record.other_cost) || 0
					const totalCost = teaPickingCost + laborCost + otherCost

					return {
						id: record.id,
						date: record.date,
						price: record.isPlaceholder ? null : (parseFloat(record.selling_price) || 0),
						production: parseFloat(record.production) || parseFloat(record.dailyTotalProduction) || 0,
						cost: totalCost,
						customer: customerDisplay,
						otherIncome: otherIncome,
						teaPickingCost: teaPickingCost,
						laborCost: laborCost,
						otherCost: otherCost,
						salesRecord: record,
						salesRecords: record.salesRecords,
						workRecords: record.workRecords,
						worker_names: record.worker_names,
						dailyTotalProduction: record.dailyTotalProduction,
						dailyTotalCost: record.dailyTotalCost,
						isPlaceholder: record.isPlaceholder || false,
						isAggregated: record.isAggregated || false,
						hasActualSales: record.hasActualSales !== false
					}
				})

				// 使用多种方式确保响应式更新
				// 方法1: 清空数组后重新填充
				this.records.splice(0, this.records.length, ...newRecords)

				// 方法2: 如果方法1不行，直接替换
				if (this.records.length !== newRecords.length) {
					this.records = newRecords
				}

				// 强制触发响应式更新
				this.$nextTick(() => {
					console.log('🔄 [收入页面] nextTick中的records长度:', this.records.length)
					console.log('🔄 [收入页面] nextTick中的filteredData长度:', this.filteredData.length)
				})

				console.log('✅ [收入页面] 销售记录数据加载完成，记录数:', this.records.length)
				console.log('📊 [收入页面] 转换后的records详情:', this.records.map(r => ({
					id: r.id,
					date: r.date,
					production: r.production,
					customer: r.customer,
					isPlaceholder: r.isPlaceholder
				})))
				console.log('📊 [收入页面] filteredData计算结果长度:', this.filteredData.length)

			} catch (error) {
				console.error('加载销售记录数据失败:', error)

				uni.showToast({
					title: '数据加载失败',
					icon: 'error',
					duration: 2000
				})
				this.records = []
			} finally {
				this.loading = false
				this.isLoadingData = false
			}
		},

		// 手动刷新数据（调试用）
		async manualRefresh() {
			console.log('🔄 [收入页面] 手动刷新数据')
			try {
				await this.loadSalesRecords()
				uni.showToast({
					title: '数据已刷新',
					icon: 'success',
					duration: 1000
				})
			} catch (error) {
				console.error('❌ [收入页面] 手动刷新失败:', error)
				uni.showToast({
					title: '刷新失败',
					icon: 'error',
					duration: 1000
				})
			}
		},

		// 格式化日期为 MM-DD
		formatDate(dateString) {
			if (!dateString) return ''
			const date = new Date(dateString)
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${month}-${day}`
		},

		// 获取当前月份的开始日期
		getCurrentMonthStart() {
			const now = new Date()
			const year = now.getFullYear()
			const month = now.getMonth()
			return new Date(year, month, 1).toISOString().split('T')[0]
		},

		// 获取当前月份的结束日期
		getCurrentMonthEnd() {
			const now = new Date()
			const year = now.getFullYear()
			const month = now.getMonth()
			return new Date(year, month + 1, 0).toISOString().split('T')[0]
		},

		// 设置当前月份日期（不触发数据加载）
		setCurrentMonthDates() {
			const today = new Date()
			const firstDay = new Date(today.getFullYear(), today.getMonth(), 1)
			const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0)

			this.startDate = this.formatDateForInput(firstDay)
			this.endDate = this.formatDateForInput(lastDay)
		},

		// 重置到当前月份
		async resetToCurrentMonth() {
			this.setCurrentMonthDates()

			// 重新加载数据（如果销售管理器已初始化）
			if (this.salesManager) {
				await this.loadSalesRecords()
			}
		},

		// 处理日期范围变化
		async handleDateRangeChange() {
			if (this.startDate && this.endDate && this.salesManager) {
				await this.loadSalesRecords()
			}
		},





		// 跳转到详情页
		goToDetail(item) {
			uni.navigateTo({
				url: `/pages/income/detail?id=${item.id}&data=${encodeURIComponent(JSON.stringify(item))}`
			})
		},

		// 处理记录更新
		async handleRecordUpdate(eventData) {
			console.log('🔄 [收入页面] 收到数据更新事件:', eventData)
			console.log('🔄 [收入页面] 当前records数组长度:', this.records.length)
			console.log('🔄 [收入页面] 当前filteredData长度:', this.filteredData.length)

			// 确保销售管理器已初始化
			if (!this.salesManager) {
				console.log('❌ [收入页面] 销售管理器未初始化，无法处理更新事件')
				uni.showToast({
					title: '系统未就绪',
					icon: 'error'
				})
				return
			}

			// 如果正在加载数据，避免重复操作
			if (this.isLoadingData) {
				console.log('⏳ [收入页面] 数据正在加载中，跳过更新事件处理')
				return
			}

			try {
				console.log('🚀 [收入页面] 开始处理数据更新事件')
				console.log('📊 [收入页面] 事件数据:', eventData)

				// 检查是否为自动保存事件
				if (eventData.autoSaved) {
					console.log('🤖 [收入页面] 检测到自动保存事件，处理销售记录更新')
				}

				// 检查是否为级联删除事件
				if (eventData.type === 'workRecordDeleted' && eventData.cascadeDeletedSalesRecords) {
					console.log('🗑️ [收入页面] 检测到级联删除事件，已删除销售记录数量:', eventData.cascadeDeletedSalesRecords.length)
				}
				console.log('📊 [收入页面] 更新前records:', this.records.map(r => ({
					id: r.id,
					date: r.date,
					production: r.production,
					customer: r.customer,
					dailyTotalProduction: r.dailyTotalProduction
				})))

				// 重新加载数据
				console.log('🔄 [收入页面] 开始重新加载销售记录数据')
				await this.loadSalesRecords()

				console.log('📊 [收入页面] 更新后records:', this.records.map(r => ({
					id: r.id,
					date: r.date,
					production: r.production,
					customer: r.customer,
					dailyTotalProduction: r.dailyTotalProduction
				})))
				console.log('📊 [收入页面] 更新后filteredData长度:', this.filteredData.length)

				// 强制触发视图更新 - 多重保险机制
				this.$forceUpdate()
				console.log('🔄 [收入页面] 已强制触发视图更新')

				// 使用 nextTick 确保 DOM 更新完成
				await this.$nextTick()
				console.log('🔄 [收入页面] nextTick 完成，DOM已更新')

				// 强制更新计算属性
				this.summary = this.calculateSummary(this.filteredData)
				console.log('🔄 [收入页面] 已强制更新汇总数据:', this.summary)

				// 再次检查数据状态
				console.log('🔍 [收入页面] 最终检查 - records长度:', this.records.length)
				console.log('🔍 [收入页面] 最终检查 - filteredData长度:', this.filteredData.length)
				console.log('🔍 [收入页面] 最终检查 - 总产量:', this.summary.totalProduction)

				// 显示更新成功提示
				uni.showToast({
					title: '数据已更新',
					icon: 'success',
					duration: 1500
				})

				console.log('✅ [收入页面] 数据更新事件处理完成')

			} catch (error) {
				console.error('❌ [收入页面] 处理数据更新事件失败:', error)

				uni.showToast({
					title: '数据更新失败',
					icon: 'error',
					duration: 2000
				})
			}
		},

		// 格式化日期为 YYYY-MM-DD
		formatDateForInput(date) {
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},

		// 开始日期变化
		async onStartDateChange(e) {
			this.startDate = e.detail.value
			await this.handleDateRangeChange()
		},

		// 结束日期变化
		async onEndDateChange(e) {
			this.endDate = e.detail.value
			await this.handleDateRangeChange()
		},

		// 获取工人信息显示文本
		getWorkerInfo(item) {
			if (item.isPlaceholder) {
				return '-'
			}

			if (item.workRecords && item.workRecords.length > 0) {
				const workerCount = item.workRecords.length
				if (workerCount === 1) {
					return item.workRecords[0].worker_name
				} else {
					return `${workerCount}位工人`
				}
			}

			return item.worker_names || '-'
		},

		// 获取完整客户信息显示文本 - 显示所有客户名称
		getCompleteCustomerInfo(item) {
			if (item.isPlaceholder) {
				return '待填写'
			}

			if (item.salesRecords && item.salesRecords.length > 0) {
				// 提取所有客户名称，去重并过滤空值
				const customerNames = item.salesRecords
					.map(record => record.customer_name)
					.filter(name => name && name.trim() !== '')
					.filter((name, index, arr) => arr.indexOf(name) === index) // 去重

				if (customerNames.length === 0) {
					return '未设置'
				} else if (customerNames.length === 1) {
					return customerNames[0]
				} else if (customerNames.length <= 3) {
					// 3个或以下客户，直接显示所有名称
					return customerNames.join('、')
				} else {
					// 超过3个客户，显示前2个加省略号
					return `${customerNames.slice(0, 2).join('、')}等${customerNames.length}位客户`
				}
			}

			return item.customer || '未设置'
		},

		// 获取平均单价显示文本
		getAveragePrice(item) {
			if (item.isPlaceholder) {
				return '-'
			}

			if (item.salesRecords && item.salesRecords.length > 0) {
				const totalIncome = item.salesRecords.reduce((sum, record) => {
					return sum + ((parseFloat(record.selling_price) || 0) * (parseFloat(record.production) || 0))
				}, 0)
				const totalProduction = item.salesRecords.reduce((sum, record) => {
					return sum + (parseFloat(record.production) || 0)
				}, 0)

				if (totalProduction > 0) {
					return `${(totalIncome / totalProduction).toFixed(2)}元/斤`
				}
			}

			const price = parseFloat(item.price) || 0
			return price > 0 ? `${price.toFixed(2)}元/斤` : '-'
		},

		// 收入列表显示辅助方法 - 支持现代化收入记录展示

		// 格式化日期为月-日简短格式 (MM-DD)
		formatDateToShortDisplay(dateString) {
			if (!dateString) return ''
			const date = new Date(dateString)
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${month}-${day}`
		},

		// 格式化日期为星期显示
		formatDateToWeekdayDisplay(dateString) {
			if (!dateString) return ''
			const date = new Date(dateString)
			const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
			return weekdays[date.getDay()]
		},

		// 获取收入记录的盈亏状态分类
		getIncomeRecordProfitStatus(item) {
			if (item.isPlaceholder) return 'placeholder'
			const profit = this.calculateRecordProfit(item)
			if (profit > 0) return 'profitable'
			if (profit < 0) return 'loss'
			return 'neutral'
		},

		// 获取收入记录状态显示图标
		getRecordStatusIcon(item) {
			if (item.isPlaceholder) return '📝'
			const profit = this.calculateRecordProfit(item)
			if (profit > 0) return '📈'
			if (profit < 0) return '📉'
			return '➖'
		},

		// 获取收入记录状态显示文字
		getRecordStatusText(item) {
			if (item.isPlaceholder) return '待填写'
			const profit = this.calculateRecordProfit(item)
			if (profit > 0) return '盈利'
			if (profit < 0) return '亏损'
			return '平衡'
		},

		// 计算收入记录的总收入金额
		calculateRecordTotalIncome(item) {
			if (item.isPlaceholder) return '0.00'
			const income = (parseFloat(item.price) || 0) * (parseFloat(item.production) || 0)
			return income.toFixed(2)
		},

		// 计算收入记录的利润金额
		calculateRecordProfit(item) {
			if (item.isPlaceholder) return '0.00'
			const income = (parseFloat(item.price) || 0) * (parseFloat(item.production) || 0)
			const totalCost = this.calculateItemTotalCost(item)
			return (income - totalCost).toFixed(2)
		},

		// 计算收入记录的利润百分比
		calculateRecordProfitPercentage(item) {
			if (item.isPlaceholder) return '0.0'
			const income = (parseFloat(item.price) || 0) * (parseFloat(item.production) || 0)
			if (income === 0) return '0.0'
			const profit = parseFloat(this.calculateRecordProfit(item))
			return ((profit / income) * 100).toFixed(1)
		},

		// 分别计算采茶成本和时工成本
		calculateSeparateCosts(record) {
			let teaPickingCost = 0
			let laborCost = 0

			// 如果有工作记录，按工作模式分别计算
			if (record.workRecords && Array.isArray(record.workRecords)) {
				record.workRecords.forEach(workRecord => {
					const earnings = parseFloat(workRecord.total_earnings) || 0
					if (workRecord.work_mode === 'tea_picking') {
						teaPickingCost += earnings
					} else if (workRecord.work_mode === 'hourly') {
						laborCost += earnings
					}
				})
			} else {
				// 向后兼容：如果没有详细的工作记录，使用总成本作为采茶成本
				const totalCost = parseFloat(record.dailyTotalCost) || parseFloat(record.total_cost) || 0
				teaPickingCost = totalCost
				laborCost = 0
			}

			return {
				teaPickingCost: teaPickingCost,
				laborCost: laborCost
			}
		},

		// 计算单个记录的总成本
		calculateItemTotalCost(item) {
			const teaPickingCost = parseFloat(item.teaPickingCost) || 0
			const laborCost = parseFloat(item.laborCost) || 0
			const otherCost = parseFloat(item.otherCost) || 0
			return teaPickingCost + laborCost + otherCost
		},

		// 获取收入记录利润状态的CSS样式类
		getRecordProfitStyleClass(item) {
			if (item.isPlaceholder) return 'placeholder'
			const profit = parseFloat(this.calculateRecordProfit(item))
			if (profit > 0) return 'positive'
			if (profit < 0) return 'negative'
			return 'neutral'
		},

		// 获取收入记录的工人数量显示
		getRecordWorkerCount(item) {
			if (item.isPlaceholder) return '0'
			// 这里可以根据实际数据结构调整
			return '1' // 临时返回1，实际应该从数据中获取
		},

		// 获取收入记录的简短平均单价显示
		getRecordAveragePriceDisplay(item) {
			if (item.isPlaceholder) return '0.00'

			if (item.salesRecords && item.salesRecords.length > 0) {
				const totalIncome = item.salesRecords.reduce((sum, record) => {
					return sum + ((parseFloat(record.selling_price) || 0) * (parseFloat(record.production) || 0))
				}, 0)
				const totalProduction = item.salesRecords.reduce((sum, record) => {
					return sum + (parseFloat(record.production) || 0)
				}, 0)

				if (totalProduction > 0) {
					return (totalIncome / totalProduction).toFixed(2)
				}
			}

			const price = parseFloat(item.price) || 0
			return price.toFixed(2)
		},
	}
}
</script>

<style scoped>
.income-management-page {
	min-height: 100vh;
	background-color: #f5f7fa;
	color: #333;
	font-size: 28rpx;
	line-height: 1.5;
	padding-bottom: 40rpx;
}

.app-header {
	background: linear-gradient(135deg, #2e7d32, #2e7d32);
	color: white;
	padding: 40rpx 30rpx;
	border-radius: 0 0 40rpx 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(46, 125, 50, 0.2);
	margin-bottom: 40rpx;
	position: relative;
	overflow: hidden;
}

.app-header::before {
	content: '';
	position: absolute;
	top: -100rpx;
	right: -100rpx;
	width: 400rpx;
	height: 400rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.app-header::after {
	content: '';
	position: absolute;
	bottom: -60rpx;
	left: -60rpx;
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.header-content {
	position: relative;
	z-index: 2;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.header-title {
	flex: 1;
}

.main-title {
	font-size: 44rpx;
	font-weight: 600;
	margin-bottom: 10rpx;
	display: block;
}

.sub-title {
	font-size: 28rpx;
	opacity: 0.9;
	display: block;
}

.user-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-icon {
	font-size: 48rpx;
}

.date-selector {
	background: white;
	border-radius: 12rpx;
	padding: 25rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
	margin-bottom: 20rpx;
}

.date-title {
	margin-bottom: 15rpx;
}

.date-label {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.date-controls {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex-wrap: wrap;
}

.date-input {
	padding: 20rpx 30rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
	font-size: 28rpx;
	flex: 1;
	min-width: 0;
	background: #f9f9f9;
	text-align: center;
}

.date-separator {
	font-size: 28rpx;
	color: #999;
}

.reset-button {
	align-self: center;
	background: #2e7d32;
	border-radius: 20rpx;
	padding: 15rpx 25rpx;
	min-width: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.reset-button:hover {
	background: #1b5e20;
	transform: translateY(-2rpx);
}

.reset-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
	text-align: center;
}

.refresh-button {
	align-self: center;
	background: #34c759;
	border-radius: 20rpx;
	padding: 15rpx 25rpx;
	min-width: 80rpx;
	margin-left: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.refresh-button:hover {
	background: #30d158;
	transform: translateY(-2rpx);
}

/* 占位符文本样式 */
.placeholder-text {
	color: #999;
	font-style: italic;
}

.btn {
	padding: 20rpx 40rpx;
	border: none;
	border-radius: 20rpx;
	cursor: pointer;
	font-weight: 500;
	transition: all 0.3s ease;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	font-size: 28rpx;
}

.btn-primary {
	background: #2e7d32;
	color: white;
}

.btn-outline {
	background: transparent;
	border: 2rpx solid #2e7d32;
	color: #2e7d32;
}

.btn-sm {
	padding: 16rpx 24rpx;
}

.cards-container {
	display: grid;
	grid-template-columns: 1fr;
	gap: 20rpx;
	padding: 0 30rpx;
	margin-bottom: 30rpx;
}

.card {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
	border: 2rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.card:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.1);
}

.profit-card {
	border-color: #2e7d32;
	background: linear-gradient(135deg, #f8fff8, #ffffff);
}

.card-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
	padding-bottom: 15rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

.card-icon {
	font-size: 42rpx;
}

.card-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.data-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	padding: 10rpx 0;
}

.data-row:last-child {
	margin-bottom: 0;
}

.data-label {
	font-size: 26rpx;
	color: #666;
}

.data-value {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.profit-value {
	font-size: 36rpx;
}

.positive {
	color: #2e7d32 !important;
	font-weight: 600 !important;
}

.negative {
	color: #d32f2f !important;
	font-weight: 600 !important;
}










/* 收入详情管理区域样式 - 现代化设计风格 */

/* 收入详情管理主容器 */
.income-details-management-section {
	margin: 0 20rpx 30rpx;
}

.income-section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 0 10rpx;
}

.section-title-group {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.section-icon {
	font-size: 32rpx;
}

.section-title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.records-count-badge {
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.3);
}

.count-display-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

/* 数据加载状态显示样式 */
.data-loading-state {
	margin: 60rpx 0;
}

.loading-status-card {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
	box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.loading-status-card .loading-status-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.loading-status-card .loading-status-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.loading-status-card .loading-status-description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

/* 空数据状态显示样式 */
.data-empty-state {
	margin: 60rpx 0;
}

.empty-status-card {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
	box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.empty-status-card .empty-status-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.empty-status-card .empty-status-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.empty-status-card .empty-status-description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

/* 收入记录列表容器 */
.income-records-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.income-record-item {
	cursor: pointer;
	transition: all 0.3s ease;
}

.income-record-item:active {
	transform: translateY(2rpx);
}

.record-display-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
	overflow: hidden;
	position: relative;
}

.income-record-item:active .record-display-card {
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 不同盈利状态的记录卡片样式 */
.income-record-item[data-profit-status="profitable"] .record-display-card {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(46, 125, 50, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(46, 125, 50, 0.03) 0%, transparent 50%);
	border-left: 6rpx solid #2e7d32;
	box-shadow: 0 8rpx 32rpx rgba(46, 125, 50, 0.15);
}

.income-record-item[data-profit-status="profitable"] .record-display-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(46, 125, 50, 0.2) 100%);
	border-radius: 0 20rpx 0 80rpx;
}

.income-record-item[data-profit-status="loss"] .record-display-card {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(211, 47, 47, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(211, 47, 47, 0.03) 0%, transparent 50%);
	border-left: 6rpx solid #d32f2f;
	box-shadow: 0 8rpx 32rpx rgba(211, 47, 47, 0.15);
}

.income-record-item[data-profit-status="loss"] .record-display-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, rgba(211, 47, 47, 0.1) 0%, rgba(211, 47, 47, 0.2) 100%);
	border-radius: 0 20rpx 0 80rpx;
}

.income-record-item[data-profit-status="placeholder"] .record-display-card {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(117, 117, 117, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(117, 117, 117, 0.03) 0%, transparent 50%);
	border-left: 6rpx solid #757575;
	box-shadow: 0 8rpx 32rpx rgba(117, 117, 117, 0.15);
}

.income-record-item[data-profit-status="placeholder"] .record-display-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, rgba(117, 117, 117, 0.1) 0%, rgba(117, 117, 117, 0.2) 100%);
	border-radius: 0 20rpx 0 80rpx;
}

/* 记录卡片点击交互效果 */
.income-record-item[data-profit-status="profitable"]:active .record-display-card {
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%, rgba(46, 125, 50, 0.12) 100%);
	box-shadow: 0 4rpx 16rpx rgba(46, 125, 50, 0.25);
}

.income-record-item[data-profit-status="loss"]:active .record-display-card {
	background: linear-gradient(135deg, rgba(211, 47, 47, 0.08) 0%, rgba(211, 47, 47, 0.12) 100%);
	box-shadow: 0 4rpx 16rpx rgba(211, 47, 47, 0.25);
}

.income-record-item[data-profit-status="placeholder"]:active .record-display-card {
	background: linear-gradient(135deg, rgba(117, 117, 117, 0.08) 0%, rgba(117, 117, 117, 0.12) 100%);
	box-shadow: 0 4rpx 16rpx rgba(117, 117, 117, 0.25);
}

/* 记录卡片头部区域 */
.record-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25rpx;
	position: relative;
	z-index: 1;
}

.record-date-display-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.date-display-icon {
	font-size: 28rpx;
	color: #666;
}

.date-display-info {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.date-display-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.2;
}

.weekday-display-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.2;
}

.profit-status-indicator-badge {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	border-radius: 30rpx;
	font-size: 26rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 1;
}

.profit-status-indicator-badge.profitable {
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.3);
}

.profit-status-indicator-badge.loss {
	background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(211, 47, 47, 0.3);
}

.profit-status-indicator-badge.placeholder {
	background: linear-gradient(135deg, #757575 0%, #9e9e9e 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(117, 117, 117, 0.3);
}

.profit-status-indicator-badge.neutral {
	background: linear-gradient(135deg, #616161 0%, #757575 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(97, 97, 97, 0.3);
}

.status-indicator-icon {
	font-size: 24rpx;
}

.status-indicator-text {
	font-size: 26rpx;
	font-weight: 600;
}

/* 记录内容区域 */
.record-content-area {
	margin-bottom: 25rpx;
	position: relative;
	z-index: 1;
}

/* 基础业务信息摘要 */
.basic-business-info-summary {
	margin-bottom: 20rpx;
}

.business-info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.business-info-card {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(233, 236, 239, 0.8) 100%);
}

.primary-financial-card {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(46, 125, 50, 0.2);
	transition: all 0.3s ease;
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%, rgba(46, 125, 50, 0.12) 100%);
}

.business-info-icon {
	font-size: 28rpx;
	color: #666;
	opacity: 0.8;
}

.business-info-details {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
	flex: 1;
}

.business-info-label {
	font-size: 30rpx;
	color: #666;
	line-height: 1.2;
}

.business-info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	line-height: 1.2;
}

.primary-financial-icon {
	font-size: 28rpx;
	color: #2e7d32;
}

.primary-financial-info {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
	flex: 1;
}

.primary-financial-label {
	font-size: 30rpx;
	color: #666;
	line-height: 1.2;
}

.primary-financial-value {
	font-size: 28rpx;
	font-weight: 600;
	line-height: 1.2;
}

.primary-financial-value.income {
	color: #2e7d32;
}

.customer-information-display {
	text-align: center;
	padding: 8rpx 16rpx;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.9) 100%);
	border-radius: 20rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.customer-information-text {
	font-size: 30rpx;
	font-weight: 500;
}

.customer-information-text.positive {
	color: #2e7d32;
}

.customer-information-text.negative {
	color: #d32f2f;
}

.customer-information-text.neutral {
	color: #666;
}

.customer-information-text.placeholder {
	color: #999;
}

/* 财务数据详情摘要 */
.financial-data-details-summary {
	margin-bottom: 20rpx;
}

.financial-data-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15rpx;
}

/* 记录摘要底部区域 */
.record-summary-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 25rpx;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.9) 100%);
	border-radius: 16rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 1;
}

.profit-summary-display {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.profit-summary-label {
	font-size: 30rpx;
	color: #666;
	font-weight: 500;
}

.profit-summary-value {
	font-size: 36rpx;
	font-weight: 700;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.profit-summary-value.positive {
	color: #2e7d32;
}

.profit-summary-value.negative {
	color: #d32f2f;
}

.profit-summary-value.neutral {
	color: #666;
}

.profit-summary-value.placeholder {
	color: #999;
}

.detail-navigation-button {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	border-radius: 25rpx;
	box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.3);
}

.navigation-button-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

.navigation-button-icon {
	font-size: 20rpx;
	color: white;
	font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.enhanced-income-section {
		margin-left: 15rpx;
		margin-right: 15rpx;
	}

	.income-card {
		padding: 25rpx;
	}

	.financial-grid,
	.details-grid {
		grid-template-columns: 1fr;
		gap: 12rpx;
	}

	.enhanced-income-footer {
		flex-direction: column;
		gap: 15rpx;
		align-items: stretch;
	}

	.view-detail-btn {
		justify-content: center;
	}

	.profit-status-badge {
		padding: 10rpx 16rpx;
		font-size: 24rpx;
	}

	.date-text {
		font-size: 30rpx;
	}

	.weekday-text {
		font-size: 22rpx;
	}

	.financial-value {
		font-size: 26rpx;
	}

	.summary-value {
		font-size: 32rpx;
	}
}

/* 大屏设备优化 */
@media (min-width: 1200rpx) {
	.enhanced-income-list {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
	}

	.financial-grid,
	.details-grid {
		grid-template-columns: 1fr 1fr;
	}
}
</style>
