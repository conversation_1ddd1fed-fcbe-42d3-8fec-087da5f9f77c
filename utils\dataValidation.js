/**
 * 数据验证工具类
 * 用于验证茶叶生产记录数据的完整性和有效性
 */

class DataValidation {
	
	/**
	 * 验证时间段字段
	 * @param {string} timePeriod - 时间段值
	 * @returns {Object} 验证结果
	 */
	static validateTimePeriod(timePeriod) {
		const validTimePeriods = ['morning', 'afternoon']
		const result = {
			isValid: false,
			normalizedValue: null,
			message: ''
		}

		if (!timePeriod) {
			result.message = '时间段不能为空'
			return result
		}

		// 标准化时间段值
		const normalizedTimePeriod = timePeriod.toLowerCase().trim()

		// 检查是否为有效的时间段
		if (validTimePeriods.includes(normalizedTimePeriod)) {
			result.isValid = true
			result.normalizedValue = normalizedTimePeriod
			result.message = '时间段验证通过'
		} else if (normalizedTimePeriod === 'noon') {
			// 历史数据兼容：将中午映射到下午
			result.isValid = true
			result.normalizedValue = 'afternoon'
			result.message = '检测到历史中午时间段，已自动映射到下午'
			console.warn('⚠️ 历史数据兼容：noon -> afternoon')
		} else {
			result.message = `无效的时间段值: ${timePeriod}，只支持 morning 或 afternoon`
		}

		return result
	}

	/**
	 * 验证茶叶采摘详情数组
	 * @param {Array} teaPickingDetails - 茶叶采摘详情数组
	 * @returns {Object} 验证结果
	 */
	static validateTeaPickingDetails(teaPickingDetails) {
		const result = {
			isValid: true,
			normalizedData: [],
			errors: [],
			warnings: []
		}

		if (!Array.isArray(teaPickingDetails)) {
			result.isValid = false
			result.errors.push('茶叶采摘详情必须是数组格式')
			return result
		}

		if (teaPickingDetails.length === 0) {
			result.isValid = false
			result.errors.push('茶叶采摘详情不能为空')
			return result
		}

		teaPickingDetails.forEach((detail, index) => {
			const detailValidation = this.validateSingleDetail(detail, index)
			
			if (!detailValidation.isValid) {
				result.isValid = false
				result.errors.push(...detailValidation.errors)
			}

			result.warnings.push(...detailValidation.warnings)
			result.normalizedData.push(detailValidation.normalizedData)
		})

		return result
	}

	/**
	 * 验证单个采摘详情
	 * @param {Object} detail - 单个采摘详情
	 * @param {number} index - 索引
	 * @returns {Object} 验证结果
	 */
	static validateSingleDetail(detail, index) {
		const result = {
			isValid: true,
			normalizedData: { ...detail },
			errors: [],
			warnings: []
		}

		// 验证时间段
		const timePeriodValidation = this.validateTimePeriod(detail.time_period)
		if (!timePeriodValidation.isValid) {
			result.isValid = false
			result.errors.push(`第${index + 1}条记录：${timePeriodValidation.message}`)
		} else {
			result.normalizedData.time_period = timePeriodValidation.normalizedValue
			if (timePeriodValidation.message.includes('映射')) {
				result.warnings.push(`第${index + 1}条记录：${timePeriodValidation.message}`)
			}
		}

		// 验证重量字段
		if (detail.actual_weight !== undefined) {
			const weight = parseFloat(detail.actual_weight)
			if (isNaN(weight) || weight < 0) {
				result.isValid = false
				result.errors.push(`第${index + 1}条记录：实际重量必须是非负数`)
			} else {
				result.normalizedData.actual_weight = weight
			}
		}

		// 验证单价字段
		if (detail.unit_price !== undefined) {
			const price = parseFloat(detail.unit_price)
			if (isNaN(price) || price < 0) {
				result.isValid = false
				result.errors.push(`第${index + 1}条记录：单价必须是非负数`)
			} else {
				result.normalizedData.unit_price = price
			}
		}

		// 验证收入字段
		if (detail.earnings !== undefined) {
			const earnings = parseFloat(detail.earnings)
			if (isNaN(earnings) || earnings < 0) {
				result.isValid = false
				result.errors.push(`第${index + 1}条记录：收入必须是非负数`)
			} else {
				result.normalizedData.earnings = earnings
			}
		}

		return result
	}

	/**
	 * 验证工作记录数据
	 * @param {Object} workRecord - 工作记录数据
	 * @returns {Object} 验证结果
	 */
	static validateWorkRecord(workRecord) {
		const result = {
			isValid: true,
			normalizedData: { ...workRecord },
			errors: [],
			warnings: []
		}

		// 验证必填字段
		if (!workRecord.worker_name) {
			result.isValid = false
			result.errors.push('工人姓名不能为空')
		}

		if (!workRecord.date) {
			result.isValid = false
			result.errors.push('日期不能为空')
		} else {
			// 验证日期格式
			const dateRegex = /^\d{4}-\d{2}-\d{2}$/
			if (!dateRegex.test(workRecord.date)) {
				result.isValid = false
				result.errors.push('日期格式必须为 YYYY-MM-DD')
			}
		}

		// 验证茶叶采摘详情
		if (workRecord.tea_picking_details) {
			const detailsValidation = this.validateTeaPickingDetails(workRecord.tea_picking_details)
			if (!detailsValidation.isValid) {
				result.isValid = false
				result.errors.push(...detailsValidation.errors)
			}
			result.warnings.push(...detailsValidation.warnings)
			result.normalizedData.tea_picking_details = detailsValidation.normalizedData
		}

		return result
	}

	/**
	 * 获取支持的时间段列表
	 * @returns {Array} 支持的时间段数组
	 */
	static getSupportedTimePeriods() {
		return ['morning', 'afternoon']
	}

	/**
	 * 获取时间段显示文本映射
	 * @returns {Object} 时间段显示文本映射
	 */
	static getTimePeriodDisplayMap() {
		return {
			morning: '上午',
			afternoon: '下午'
		}
	}

	/**
	 * 批量验证多条工作记录
	 * @param {Array} workRecords - 工作记录数组
	 * @returns {Object} 批量验证结果
	 */
	static validateWorkRecords(workRecords) {
		const result = {
			isValid: true,
			normalizedData: [],
			errors: [],
			warnings: [],
			summary: {
				total: 0,
				valid: 0,
				invalid: 0,
				withWarnings: 0
			}
		}

		if (!Array.isArray(workRecords)) {
			result.isValid = false
			result.errors.push('工作记录必须是数组格式')
			return result
		}

		result.summary.total = workRecords.length

		workRecords.forEach((record, index) => {
			const recordValidation = this.validateWorkRecord(record)
			
			if (recordValidation.isValid) {
				result.summary.valid++
				result.normalizedData.push(recordValidation.normalizedData)
			} else {
				result.isValid = false
				result.summary.invalid++
				result.errors.push(`记录${index + 1}：${recordValidation.errors.join(', ')}`)
			}

			if (recordValidation.warnings.length > 0) {
				result.summary.withWarnings++
				result.warnings.push(`记录${index + 1}：${recordValidation.warnings.join(', ')}`)
			}
		})

		return result
	}
}

export default DataValidation
