<template>
	<view class="add-record-page">
		<!-- 权限检查 -->
		<view class="permission-error" v-if="!isAdmin">
			<text class="error-icon">🚫</text>
			<text class="error-title">权限不足</text>
			<text class="error-desc">只有管理员可以录入工作记录</text>
			<button class="back-btn" @click="goBack">返回</button>
		</view>

		<!-- 录入表单 -->
		<view class="form-container" v-else>
			<!-- 工作模式切换 -->
			<WorkModeSwitch v-model="formData.workMode" @change="onModeChange" />

			<!-- 通用字段 -->
			<view class="form-section">
				<view class="section-title">基本信息</view>

				<view class="form-item">
					<text class="form-label">工人姓名</text>
					<view class="name-input-wrapper">
						<input class="form-input" type="text" placeholder="请输入工人姓名" v-model="formData.workerName"
							@input="onNameInput" @focus="onNameFocus" />
						<text class="input-icon" @click="showNameLibrary">📝</text>
					</view>

					<!-- 姓名建议列表 -->
					<view class="name-suggestions" v-if="showNameSuggestions && filteredNames.length > 0">
						<view class="suggestion-item" v-for="name in filteredNames" :key="name"
							@click="selectName(name)">
							<text>{{ name }}</text>
						</view>
					</view>
				</view>

				<view class="form-item">
					<text class="form-label">工作日期</text>
					<picker mode="date" :value="formData.date" :end="todayDate" @change="onDateChange">
						<view class="picker-input">
							<text>{{ formData.date || '请选择日期' }}</text>
							<text class="picker-arrow">📅</text>
						</view>
					</picker>
				</view>

				<!-- 姓名建议遮罩层 -->
				<view class="suggestions-overlay" v-if="showNameSuggestions && filteredNames.length > 0"
					@click="hideNameSuggestions"></view>
			</view>

			<!-- 采茶模式字段 -->
			<view class="form-section" v-if="formData.workMode === 'tea_picking'">
				<!-- 采茶记录列表 -->
				<view class="tea-records">
					<view v-for="(record, index) in teaRecords" :key="index" class="tea-record">
						<view class="record-header">
							<text class="record-title">🍃 采茶信息 {{ index + 1 }}</text>
							<text class="delete-btn" @click="confirmDeleteRecord(index)" v-if="teaRecords.length > 1">
								🗑️ 删除
							</text>
						</view>

						<view class="tea-form-item">
							<text class="tea-label">⚖️ 原斤数</text>
							<input class="tea-input" type="digit" v-model="record.originalWeight" placeholder="请输入原斤数"
								@input="calculateWeight(index)" />
							<text class="tea-unit">斤</text>
						</view>

						<view class="tea-form-item">
							<text class="tea-label">⏰ 时间段</text>
							<view class="time-period-buttons">
								<view class="time-period-btn" :class="{ active: record.timeIndex === 0 }"
									@click="selectTimePeriod(index, 0)">
									<text class="btn-icon">🌅</text>
									<text class="btn-text">上午</text>
								</view>
								<view class="time-period-btn" :class="{ active: record.timeIndex === 1 }"
									@click="selectTimePeriod(index, 1)">
									<text class="btn-icon">🌇</text>
									<text class="btn-text">下午</text>
								</view>
							</view>
						</view>



						<view class="tea-form-item">
							<text class="tea-label">🌱 类型</text>
							<view class="tea-type-buttons">
								<view class="tea-type-btn" :class="{ active: record.typeIndex === 0 }"
									@click="selectTeaType(index, 0)">
									<text class="btn-icon">🍃</text>
									<text class="btn-text">一叶</text>
								</view>
								<view class="tea-type-btn" :class="{ active: record.typeIndex === 1 }"
									@click="selectTeaType(index, 1)">
									<text class="btn-icon">🌿</text>
									<text class="btn-text">二叶</text>
								</view>
								<view class="tea-type-btn" :class="{ active: record.typeIndex === 2 }"
									@click="selectTeaType(index, 2)">
									<text class="btn-icon">🍀</text>
									<text class="btn-text">三叶</text>
								</view>
							</view>
						</view>

						<view class="tea-form-item">
							<text class="tea-label">💰 单价</text>
							<input class="tea-input" type="digit" v-model="record.price" placeholder="请输入单价"
								@input="calculateMoney(index)" />
							<text class="tea-unit">元/斤</text>
						</view>

						<view class="tea-form-item">
							<text class="tea-label">💧 扣除水分</text>
							<input class="tea-input" type="digit" v-model="record.waterLoss" placeholder="选填，例：5"
								@input="calculateWeight(index)" />
							<text class="tea-unit">%</text>
						</view>

						<view class="tea-form-item">
							<text class="tea-label">📏 实际斤数</text>
							<view class="tea-readonly-input">
								<text class="tea-readonly-value">{{ record.finalWeight }}</text>
							</view>
							<text class="tea-unit">斤</text>
						</view>

						<view class="tea-form-item">
							<text class="tea-label">💵 工钱</text>
							<view class="tea-readonly-input earnings-input">
								<text class="tea-readonly-value earnings">{{ record.money }}</text>
							</view>
							<text class="tea-unit">元</text>
						</view>
					</view>
				</view>

				<!-- 添加按钮 -->
				<view class="add-record-btn" @click="confirmAddRecord">
					<text class="add-btn-icon">➕</text>
					<text class="add-btn-text">新增一条采茶记录</text>
				</view>

				<!-- 汇总信息 -->
				<view class="tea-summary" v-if="hasValidTeaData">
					<view class="summary-header">
						<text class="summary-title">📊 汇总统计</text>
					</view>
					<view class="summary-grid">
						<view class="summary-item">
							<view class="item-left">
								<view class="item-icon-wrapper">
									<text class="summary-icon">📏</text>
								</view>
								<view class="item-content">
									<text class="summary-label">总斤数</text>
								</view>
							</view>
							<view class="item-right">
								<text class="summary-value">{{ totalWeight }}</text>
								<text class="summary-unit">斤</text>
							</view>
						</view>
						<view class="summary-item">
							<view class="item-left">
								<view class="item-icon-wrapper">
									<text class="summary-icon">💰</text>
								</view>
								<view class="item-content">
									<text class="summary-label">平均单价</text>
								</view>
							</view>
							<view class="item-right">
								<text class="summary-value">{{ averagePrice }}</text>
								<text class="summary-unit">元/斤</text>
							</view>
						</view>
						<view class="summary-item total-earnings-item">
							<view class="item-left">
								<view class="item-icon-wrapper earnings-icon">
									<text class="summary-icon">💴</text>
								</view>
								<view class="item-content">
									<text class="summary-label">总工钱</text>
								</view>
							</view>
							<view class="item-right">
								<text class="summary-value total-earnings">{{ totalMoney }}</text>
								<text class="summary-unit earnings-unit">元</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 时工模式字段 -->
			<view class="hourly-section" v-if="formData.workMode === 'hourly'">
				<view class="section-title">⏰ 时工信息</view>

				<!-- 简化模式 -->
				<view class="hourly-form-group" :class="{ disabled: formData.hourly.isDetailMode }">
					<view class="hourly-form-row">
						<view class="hourly-label">📅 一天</view>
						<view class="hourly-input-group">
							<view class="hourly-input-wrapper">
								<input class="hourly-input" type="digit" placeholder="工时"
									v-model="formData.hourly.simple.workHours" :disabled="formData.hourly.isDetailMode"
									@input="calculateHourlyEarnings" />
								<text class="hourly-unit">小时</text>
							</view>
							<view class="hourly-input-wrapper">
								<input class="hourly-input" type="digit" placeholder="时薪"
									v-model="formData.hourly.simple.hourlyRate" :disabled="formData.hourly.isDetailMode"
									@input="calculateHourlyEarnings" />
								<text class="hourly-unit">元/时</text>
							</view>
						</view>
						<view class="expand-btn" @click="toggleDetailMode">
							<text class="expand-icon">{{ formData.hourly.isDetailMode ? '➖' : '➕' }}</text>
						</view>
					</view>
				</view>

				<!-- 详细模式 -->
				<view v-show="formData.hourly.isDetailMode" class="detail-form">
					<!-- 上午 -->
					<view class="hourly-form-row">
						<view class="hourly-label">🌅 上午</view>
						<view class="hourly-input-group">
							<view class="hourly-input-wrapper">
								<input class="hourly-input" type="digit" placeholder="工时"
									v-model="formData.hourly.detail.morning.workHours"
									@input="calculateHourlyEarnings" />
								<text class="hourly-unit">小时</text>
							</view>
							<view class="hourly-input-wrapper">
								<input class="hourly-input" type="digit" placeholder="时薪"
									v-model="formData.hourly.detail.morning.hourlyRate"
									@input="calculateHourlyEarnings" />
								<text class="hourly-unit">元/时</text>
							</view>
						</view>
					</view>

					<!-- 下午 -->
					<view class="hourly-form-row">
						<view class="hourly-label">☀️ 下午</view>
						<view class="hourly-input-group">
							<view class="hourly-input-wrapper">
								<input class="hourly-input" type="digit" placeholder="工时"
									v-model="formData.hourly.detail.afternoon.workHours"
									@input="calculateHourlyEarnings" />
								<text class="hourly-unit">小时</text>
							</view>
							<view class="hourly-input-wrapper">
								<input class="hourly-input" type="digit" placeholder="时薪"
									v-model="formData.hourly.detail.afternoon.hourlyRate"
									@input="calculateHourlyEarnings" />
								<text class="hourly-unit">元/时</text>
							</view>
						</view>
					</view>

					<!-- 加班 -->
					<view class="hourly-form-row">
						<view class="hourly-label">🌙 加班</view>
						<view class="hourly-input-group">
							<view class="hourly-input-wrapper">
								<input class="hourly-input" type="digit" placeholder="选填"
									v-model="formData.hourly.detail.overtime.workHours"
									@input="calculateHourlyEarnings" />
								<text class="hourly-unit">小时</text>
							</view>
							<view class="hourly-input-wrapper">
								<input class="hourly-input" type="digit" placeholder="选填"
									v-model="formData.hourly.detail.overtime.hourlyRate"
									@input="calculateHourlyEarnings" />
								<text class="hourly-unit">元/时</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 汇总信息 -->
				<view class="hourly-summary" v-if="hourlyTotalHours > 0">
					<view class="summary-header">
						<view class="summary-title-wrapper">
							<text class="summary-icon-large">📊</text>
							<text class="summary-title">汇总统计</text>
						</view>
					</view>
					<view class="summary-content">
						<view class="summary-grid">
							<view class="summary-item">
								<view class="item-left">
									<view class="item-icon-wrapper">
										<text class="summary-icon">⏱️</text>
									</view>
									<view class="item-content">
										<text class="summary-label">总工时</text>
									</view>
								</view>
								<view class="item-right">
									<text class="summary-value">{{ hourlyTotalHours }}</text>
									<text class="summary-unit">小时</text>
								</view>
							</view>
							<view class="summary-item">
								<view class="item-left">
									<view class="item-icon-wrapper">
										<text class="summary-icon">💰</text>
									</view>
									<view class="item-content">
										<text class="summary-label">平均时薪</text>
									</view>
								</view>
								<view class="item-right">
									<text class="summary-value">{{ hourlyAverageRate }}</text>
									<text class="summary-unit">元/时</text>
								</view>
							</view>
							<view class="summary-item total-earnings-item">
								<view class="item-left">
									<view class="item-icon-wrapper earnings-icon">
										<text class="summary-icon">💴</text>
									</view>
									<view class="item-content">
										<text class="summary-label">总工钱</text>
									</view>
								</view>
								<view class="item-right">
									<text class="summary-value total-earnings">{{ hourlyTotalEarnings }}</text>
									<text class="summary-unit earnings-unit">元</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button class="submit-btn" :class="{ disabled: !canSubmit }" :disabled="!canSubmit"
					@click="handleSubmit">
					<text v-if="!submitting">保存记录</text>
					<text v-else>保存中...</text>
				</button>
			</view>
		</view>

		<!-- 姓名库弹窗 -->
		<view class="modal-overlay" v-if="showNameLibraryModal" @click="showNameLibraryModal = false">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">选择工人姓名</text>
					<text class="modal-close" @click="showNameLibraryModal = false">✕</text>
				</view>
				<view class="name-list">
					<view class="name-item" v-for="name in getSortedNamesByUsage()" :key="name"
						@click="selectNameFromLibrary(name)">
						<view class="name-content">
							<text class="name-text">{{ name }}</text>
							<text class="usage-count" v-if="getNameUsage(name) > 0">{{ getNameUsage(name) }}次</text>
						</view>
					</view>
					<view class="empty-state" v-if="nameLibrary.length === 0">
						<text>暂无姓名记录</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import WorkModeSwitch from '@/components/WorkModeSwitch.vue'

export default {
	name: 'AddRecordPage',
	components: {
		WorkModeSwitch
	},
	data() {
		return {
			formData: {
				workMode: 'tea_picking',
				date: '',
				workerName: '',
				// 时工模式字段
				hourly: {
					isDetailMode: false,  // 是否为详细模式
					// 简化模式数据
					simple: {
						workHours: '',
						hourlyRate: ''
					},
					// 详细模式数据
					detail: {
						morning: {
							workHours: '',
							hourlyRate: ''
						},
						afternoon: {
							workHours: '',
							hourlyRate: ''
						},
						overtime: {
							workHours: '',
							hourlyRate: ''
						}
					}
				}
			},
			// 采茶记录数组
			teaRecords: [{
				timeIndex: 0,
				originalWeight: '',
				typeIndex: 1, // 默认二叶
				price: '',
				waterLoss: '',
				finalWeight: '0.00',
				money: '0.00'
			}],
			// 选项数组
			timeOptions: ['上午', '下午'],
			typeOptions: ['一叶', '二叶', '三叶'],
			// 带图标的选项数组
			timeOptionsWithIcons: ['🌅 上午', '🌇 下午'],
			typeOptionsWithIcons: ['🍃 一叶', '🌿 二叶', '🍀 三叶'],
			showNameSuggestions: false,
			showNameLibraryModal: false,
			submitting: false,
			// 姓名使用频率缓存
			nameUsageCache: {},
			nameUsageCacheTime: 0
		}
	},
	computed: {
		...mapGetters('user', ['isAdmin']),
		...mapGetters('record', ['nameLibrary']),

		todayDate() {
			return new Date().toISOString().split('T')[0]
		},

		filteredNames() {
			if (!this.nameLibrary || this.nameLibrary.length === 0) return []

			// 获取排序后的姓名列表
			const sortedNames = this.getSortedNamesByUsage()

			// 如果没有输入内容，返回所有排序后的姓名
			if (!this.formData.workerName) return sortedNames

			// 根据输入内容过滤姓名，保持排序
			return sortedNames.filter(name =>
				name.includes(this.formData.workerName)
			)
		},

		// 采茶汇总计算
		totalWeight() {
			return this.teaRecords.reduce((sum, record) => {
				return sum + (parseFloat(record.finalWeight) || 0)
			}, 0).toFixed(2)
		},

		totalMoney() {
			return this.teaRecords.reduce((sum, record) => {
				return sum + (parseFloat(record.money) || 0)
			}, 0).toFixed(2)
		},

		averagePrice() {
			const totalWeight = parseFloat(this.totalWeight)
			const totalMoney = parseFloat(this.totalMoney)
			return totalWeight > 0 ? (totalMoney / totalWeight).toFixed(2) : '0.00'
		},

		workTimeInfo() {
			const regularHours = parseFloat(this.formData.regularHours) || 0
			const overtimeHours = parseFloat(this.formData.overtimeHours) || 0
			const totalHours = regularHours + overtimeHours

			return {
				totalHours: totalHours.toFixed(2),
				regularHours: regularHours.toFixed(2),
				overtimeHours: overtimeHours.toFixed(2)
			}
		},

		// 采茶模式是否有有效数据
		hasValidTeaData() {
			if (this.teaRecords.length === 0) return false

			// 检查是否至少有一条记录包含有效数据
			return this.teaRecords.some(record => {
				const hasWeight = record.originalWeight && parseFloat(record.originalWeight) > 0
				const hasPrice = record.price && parseFloat(record.price) > 0
				return hasWeight && hasPrice
			})
		},

		// 时工总工时
		hourlyTotalHours() {
			if (!this.formData.hourly.isDetailMode) {
				// 简化模式
				return parseFloat(this.formData.hourly.simple.workHours) || 0
			} else {
				// 详细模式
				const morningHours = parseFloat(this.formData.hourly.detail.morning.workHours) || 0
				const afternoonHours = parseFloat(this.formData.hourly.detail.afternoon.workHours) || 0
				const overtimeHours = parseFloat(this.formData.hourly.detail.overtime.workHours) || 0
				return morningHours + afternoonHours + overtimeHours
			}
		},

		// 时工平均时薪
		hourlyAverageRate() {
			const totalHours = this.hourlyTotalHours
			const totalEarnings = parseFloat(this.hourlyTotalEarnings) || 0

			if (totalHours === 0) return '0.00'
			return (totalEarnings / totalHours).toFixed(2)
		},

		// 时工总工钱
		hourlyTotalEarnings() {
			if (!this.formData.hourly.isDetailMode) {
				// 简化模式计算
				const hours = parseFloat(this.formData.hourly.simple.workHours) || 0
				const rate = parseFloat(this.formData.hourly.simple.hourlyRate) || 0
				return (hours * rate).toFixed(2)
			} else {
				// 详细模式计算
				const morningHours = parseFloat(this.formData.hourly.detail.morning.workHours) || 0
				const morningRate = parseFloat(this.formData.hourly.detail.morning.hourlyRate) || 0
				const afternoonHours = parseFloat(this.formData.hourly.detail.afternoon.workHours) || 0
				const afternoonRate = parseFloat(this.formData.hourly.detail.afternoon.hourlyRate) || 0
				const overtimeHours = parseFloat(this.formData.hourly.detail.overtime.workHours) || 0
				const overtimeRate = parseFloat(this.formData.hourly.detail.overtime.hourlyRate) || 0

				const morningEarnings = morningHours * morningRate
				const afternoonEarnings = afternoonHours * afternoonRate
				const overtimeEarnings = overtimeHours * overtimeRate

				return (morningEarnings + afternoonEarnings + overtimeEarnings).toFixed(2)
			}
		},

		hourlyEarnings() {
			return this.hourlyTotalEarnings
		},

		canSubmit() {
			if (!this.formData.date || !this.formData.workerName || this.submitting) {
				console.log('基础验证失败:', {
					date: this.formData.date,
					workerName: this.formData.workerName,
					submitting: this.submitting
				})
				return false
			}

			if (this.formData.workMode === 'tea_picking') {
				// 检查是否有有效的采茶记录
				const hasValidTea = this.teaRecords.some(record => {
					return record.originalWeight &&
						record.price &&
						parseFloat(record.originalWeight) > 0 &&
						parseFloat(record.price) > 0
				})

				console.log('采茶模式验证:', {
					teaRecordsLength: this.teaRecords.length,
					hasValidTea,
					teaRecords: this.teaRecords
				})

				return hasValidTea
			} else {
				// 时工模式验证
				if (!this.formData.hourly.isDetailMode) {
					// 简化模式验证
					const simpleValid = this.formData.hourly.simple.workHours &&
						this.formData.hourly.simple.hourlyRate &&
						parseFloat(this.formData.hourly.simple.workHours) > 0 &&
						parseFloat(this.formData.hourly.simple.hourlyRate) > 0

					console.log('时工简化模式验证:', {
						workHours: this.formData.hourly.simple.workHours,
						hourlyRate: this.formData.hourly.simple.hourlyRate,
						simpleValid
					})

					return simpleValid
				} else {
					// 详细模式验证：至少有一个时间段有完整数据
					const morning = this.formData.hourly.detail.morning
					const afternoon = this.formData.hourly.detail.afternoon
					const overtime = this.formData.hourly.detail.overtime

					const morningValid = morning.workHours && morning.hourlyRate &&
						parseFloat(morning.workHours) > 0 && parseFloat(morning.hourlyRate) > 0
					const afternoonValid = afternoon.workHours && afternoon.hourlyRate &&
						parseFloat(afternoon.workHours) > 0 && parseFloat(afternoon.hourlyRate) > 0
					const overtimeValid = overtime.workHours && overtime.hourlyRate &&
						parseFloat(overtime.workHours) > 0 && parseFloat(overtime.hourlyRate) > 0

					const detailValid = morningValid || afternoonValid || overtimeValid

					console.log('时工详细模式验证:', {
						morningValid,
						afternoonValid,
						overtimeValid,
						detailValid
					})

					return detailValid
				}
			}
		}
	},

	// 添加$showToast方法的兼容性处理
	created() {
		if (!this.$showToast) {
			this.$showToast = (message, type = 'none') => {
				uni.showToast({
					title: message,
					icon: type === 'success' ? 'success' : type === 'error' ? 'error' : 'none',
					duration: 2000
				})
			}
		}
	},

	onLoad() {
		this.initForm()
		this.loadLastFormSelections()
	},
	methods: {
		...mapActions('record', ['fetchNameLibrary']),

		async initForm() {
			// 设置默认日期为今天
			this.formData.date = this.todayDate

			// 获取姓名库
			await this.fetchNameLibrary()

			// 如果姓名库为空，添加一些测试数据
			if (!this.nameLibrary || this.nameLibrary.length === 0) {
				console.log('姓名库为空，添加测试数据')
				// 这里可以调用store的action来添加默认姓名
				// 或者在store中确保有默认数据
			} else {
				// 初始化姓名使用频率缓存
				this.updateNameUsageCache()
			}

			// 加载上次保存的单价数据
			this.loadLastPriceData()
		},

		// 加载上次保存的单价数据
		loadLastPriceData() {
			try {
				// 加载采茶模式的单价
				const lastTeaPrice = uni.getStorageSync('lastTeaPrice')
				if (lastTeaPrice && this.teaRecords.length > 0) {
					this.teaRecords[0].price = lastTeaPrice
				}

				// 加载时工模式的数据
				const lastHourlyData = uni.getStorageSync('lastHourlyData')
				if (lastHourlyData) {
					this.formData.hourly = {
						...this.formData.hourly,
						...lastHourlyData
					}
				}
			} catch (error) {
				console.log('加载上次单价数据失败:', error)
			}
		},

		// 加载上次保存的表单选择
		loadLastFormSelections() {
			try {
				// 加载上次选择的时间段
				const lastTimePeriod = uni.getStorageSync('lastSelectedTimePeriod')
				if (lastTimePeriod && (lastTimePeriod === 'morning' || lastTimePeriod === 'afternoon')) {
					this.formData.timePeriod = lastTimePeriod
				}

				// 加载上次选择的项目类型
				const lastProject = uni.getStorageSync('lastSelectedProject')
				if (lastProject && ['one', 'two', 'three'].includes(lastProject)) {
					this.formData.projectType = lastProject
				}

				console.log('加载上次表单选择:', {
					timePeriod: lastTimePeriod,
					projectType: lastProject
				})
			} catch (error) {
				console.log('加载上次表单选择失败:', error)
			}
		},

		// 保存当前单价数据
		saveCurrentPriceData() {
			try {
				// 保存采茶模式的单价（取第一个有效记录的单价）
				if (this.formData.workMode === 'tea_picking') {
					const validRecord = this.teaRecords.find(record => record.price)
					if (validRecord) {
						uni.setStorageSync('lastTeaPrice', validRecord.price)
					}
				}

				// 保存时工模式的时薪
				if (this.formData.workMode === 'hourly') {
					// 保存最后使用的时薪数据
					const hourlyData = {
						isDetailMode: this.formData.hourly.isDetailMode,
						simple: this.formData.hourly.simple,
						detail: this.formData.hourly.detail
					}
					uni.setStorageSync('lastHourlyData', hourlyData)
				}
			} catch (error) {
				console.log('保存单价数据失败:', error)
			}
		},

		// 保存当前表单选择
		saveCurrentFormSelections() {
			try {
				// 保存时间段选择（只在采茶模式下保存）
				if (this.formData.workMode === 'tea_picking' && this.formData.timePeriod) {
					uni.setStorageSync('lastSelectedTimePeriod', this.formData.timePeriod)
				}

				// 保存项目类型选择（只在采茶模式下保存）
				if (this.formData.workMode === 'tea_picking' && this.formData.projectType) {
					uni.setStorageSync('lastSelectedProject', this.formData.projectType)
				}

				console.log('保存表单选择:', {
					timePeriod: this.formData.timePeriod,
					projectType: this.formData.projectType
				})
			} catch (error) {
				console.log('保存表单选择失败:', error)
			}
		},

		onModeChange(mode) {
			// 切换模式时清空相关字段，但保留单价数据
			if (mode === 'tea_picking') {
				this.formData.startTime = ''
				this.formData.endTime = ''
				// 不清空 hourlyRate 和 overtimeRate，保留上次的值

				// 加载采茶模式的上次单价
				const lastTeaPrice = uni.getStorageSync('lastTeaPrice')
				if (lastTeaPrice && !this.formData.price) {
					this.formData.price = lastTeaPrice
				}
			} else {
				this.formData.timePeriod = 'morning'
				this.formData.projectType = 'two'
				this.formData.weight = ''
				// 不清空 price，保留上次的值

				// 加载时工模式的上次时薪
				const lastHourlyRate = uni.getStorageSync('lastHourlyRate')
				const lastOvertimeRate = uni.getStorageSync('lastOvertimeRate')
				if (lastHourlyRate && !this.formData.hourlyRate) {
					this.formData.hourlyRate = lastHourlyRate
				}
				if (lastOvertimeRate && !this.formData.overtimeRate) {
					this.formData.overtimeRate = lastOvertimeRate
				}
			}
		},

		onDateChange(e) {
			this.formData.date = e.detail.value
		},

		onNameFocus() {
			if (this.nameLibrary && this.nameLibrary.length > 0) {
				this.showNameSuggestions = true
				console.log('姓名输入框获得焦点，显示建议列表', {
					showNameSuggestions: this.showNameSuggestions,
					nameLibrary: this.nameLibrary,
					filteredNames: this.filteredNames
				})
			} else {
				console.log('姓名库为空，无法显示建议列表')
			}
		},

		onNameInput() {
			if (this.nameLibrary && this.nameLibrary.length > 0) {
				this.showNameSuggestions = true
			}
		},

		selectName(name) {
			this.formData.workerName = name
			this.showNameSuggestions = false
		},

		hideNameSuggestions() {
			this.showNameSuggestions = false
		},

		showNameLibrary() {
			this.showNameLibraryModal = true
		},

		selectNameFromLibrary(name) {
			this.formData.workerName = name
			this.showNameLibraryModal = false
			this.showNameSuggestions = false
		},

		onStartTimeChange(e) {
			this.formData.startTime = e.detail.value
			this.calculateHourlyEarnings()
		},

		onEndTimeChange(e) {
			this.formData.endTime = e.detail.value
			this.calculateHourlyEarnings()
		},

		// 采茶记录管理方法
		calculateWeight(index) {
			const record = this.teaRecords[index]
			const originalWeight = parseFloat(record.originalWeight) || 0
			const waterLoss = parseFloat(record.waterLoss) || 0

			const finalWeight = originalWeight * (1 - waterLoss / 100)
			record.finalWeight = finalWeight.toFixed(2)
			this.calculateMoney(index)
		},

		calculateMoney(index) {
			const record = this.teaRecords[index]
			const finalWeight = parseFloat(record.finalWeight) || 0
			const price = parseFloat(record.price) || 0

			const money = finalWeight * price
			record.money = money.toFixed(2)
		},

		handleTimeChange(e, index) {
			this.teaRecords[index].timeIndex = parseInt(e.detail.value)
		},

		// 新增：选择时间段按钮处理方法
		selectTimePeriod(recordIndex, timeIndex) {
			this.teaRecords[recordIndex].timeIndex = timeIndex
		},

		// 新增：选择茶叶类型按钮处理方法
		selectTeaType(recordIndex, typeIndex) {
			this.teaRecords[recordIndex].typeIndex = typeIndex
		},

		handleTypeChange(e, index) {
			this.teaRecords[index].typeIndex = parseInt(e.detail.value)
		},

		// 确认添加采茶记录
		confirmAddRecord() {
			uni.showModal({
				title: '确认添加',
				content: '确定要添加新的采茶信息记录吗？',
				confirmText: '确认',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.addTeaRecord()
					}
				}
			})
		},

		addTeaRecord() {
			// 智能填充逻辑：当添加第二条记录时，自动继承第一条记录的数据
			let newRecord = {
				timeIndex: 0,
				originalWeight: '',
				typeIndex: 1, // 默认二叶
				price: '',
				waterLoss: '',
				finalWeight: '0.00',
				money: '0.00'
			}

			// 如果已有记录，则应用智能填充
			if (this.teaRecords.length > 0) {
				// 找到最近一条有效数据的记录作为参考
				const referenceRecord = this.findLastValidRecord()

				if (referenceRecord) {
					// 继承茶叶类型和单价
					if (referenceRecord.typeIndex !== undefined) {
						newRecord.typeIndex = referenceRecord.typeIndex
					}
					if (referenceRecord.price && parseFloat(referenceRecord.price) > 0) {
						newRecord.price = referenceRecord.price
					}

					// 如果有扣除水分的习惯，也继承这个设置
					if (referenceRecord.waterLoss && parseFloat(referenceRecord.waterLoss) > 0) {
						newRecord.waterLoss = referenceRecord.waterLoss
					}
				}

				// 智能设置时间段
				newRecord.timeIndex = this.getNextTimeIndex()
			}

			this.teaRecords.push(newRecord)

			// 添加记录后的智能提示
			if (this.teaRecords.length >= 2) {
				const referenceRecord = this.findLastValidRecord()
				if (referenceRecord) {
					let toastMessage = '已智能填充：'
					const filledItems = []

					if (newRecord.typeIndex === referenceRecord.typeIndex) {
						const typeText = this.typeOptionsWithIcons[newRecord.typeIndex] || '茶叶类型'
						filledItems.push(typeText)
					}
					if (newRecord.price === referenceRecord.price) {
						filledItems.push(`单价${newRecord.price}元`)
					}
					if (newRecord.waterLoss === referenceRecord.waterLoss && newRecord.waterLoss) {
						filledItems.push(`扣水${newRecord.waterLoss}%`)
					}

					if (filledItems.length > 0) {
						toastMessage += filledItems.join('、')
						uni.showToast({
							title: toastMessage,
							icon: 'none',
							duration: 2500
						})
					}
				}
			}
		},

		// 确认删除采茶记录
		confirmDeleteRecord(index) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除第${index + 1}条采茶信息记录吗？删除后无法恢复。`,
				confirmText: '确认删除',
				cancelText: '取消',
				confirmColor: '#ff4444',
				success: (res) => {
					if (res.confirm) {
						this.deleteRecord(index)
					}
				}
			})
		},

		deleteRecord(index) {
			if (this.teaRecords.length > 1) {
				this.teaRecords.splice(index, 1)
			}
		},

		// 辅助方法：获取时间段的英文key
		getTimePeriodKey(index) {
			const map = ['morning', 'afternoon']
			return map[index] || 'morning'
		},

		// 辅助方法：找到最近一条有效数据的记录
		findLastValidRecord() {
			// 从最后一条记录开始向前查找
			for (let i = this.teaRecords.length - 1; i >= 0; i--) {
				const record = this.teaRecords[i]
				// 检查记录是否有有效的茶叶类型或单价
				if ((record.typeIndex !== undefined && record.typeIndex >= 0) ||
					(record.price && parseFloat(record.price) > 0)) {
					return record
				}
			}
			// 如果没有找到有效记录，返回第一条记录
			return this.teaRecords.length > 0 ? this.teaRecords[0] : null
		},

		// 辅助方法：智能获取下一个时间段索引
		getNextTimeIndex() {
			if (this.teaRecords.length === 0) {
				return 0 // 默认上午
			}

			// 根据最后一条记录智能推测下一个时间段
			const lastRecord = this.teaRecords[this.teaRecords.length - 1]
			const lastTimeIndex = lastRecord.timeIndex

			if (lastTimeIndex === 0) { // 上午 -> 下午
				return 1
			} else { // 下午 -> 上午（新的一天）
				return 0
			}
		},

		// 辅助方法：获取项目类型的key
		getProjectKey(index) {
			const map = ['one', 'two', 'three']
			return map[index] || 'two'
		},

		// 切换详细模式
		toggleDetailMode() {
			this.formData.hourly.isDetailMode = !this.formData.hourly.isDetailMode

			if (this.formData.hourly.isDetailMode) {
				// 切换到详细模式时，清空简化模式数据
				this.formData.hourly.simple.workHours = ''
				this.formData.hourly.simple.hourlyRate = ''
			} else {
				// 切换到简化模式时，清空详细模式数据
				this.formData.hourly.detail.morning.workHours = ''
				this.formData.hourly.detail.morning.hourlyRate = ''
				this.formData.hourly.detail.afternoon.workHours = ''
				this.formData.hourly.detail.afternoon.hourlyRate = ''
				this.formData.hourly.detail.overtime.workHours = ''
				this.formData.hourly.detail.overtime.hourlyRate = ''
			}
		},

		calculateHourlyEarnings() {
			// 自动计算时工工钱（在computed中处理）
		},

		async handleSubmit() {
			if (!this.canSubmit) {
				console.log('表单验证失败，无法提交')
				return
			}

			this.submitting = true

			try {
				const recordData = this.buildRecordData()
				console.log('构建的记录数据:', recordData)

				// 调用Vuex store的addRecord action
				const result = await this.$store.dispatch('record/addRecord', recordData)
				console.log('保存结果:', result)

				if (result && result.success) {
					// 保存当前单价数据供下次使用
					this.saveCurrentPriceData()

					// 保存当前表单选择供下次使用
					this.saveCurrentFormSelections()

					// 更新姓名使用频率缓存（因为新增了记录）
					this.updateNameUsageCache()

					// 清空表单
					this.resetForm()

					// 显示成功提示并立即跳转到记录列表
					this.$showToast('记录保存成功', 'success')
					uni.switchTab({
						url: '/pages/record/list'
					})
				} else {
					const errorMessage = result?.message || '保存失败，未知错误'
					console.error('保存失败:', errorMessage)
					this.$showToast(errorMessage, 'error')
				}
			} catch (error) {
				console.error('保存记录异常:', error)
				this.$showToast(`保存失败：${error.message || '请稍后重试'}`, 'error')
			} finally {
				this.submitting = false
			}
		},

		buildRecordData() {
			const baseData = {
				date: this.formData.date,
				worker_name: this.formData.workerName.trim(),
				work_mode: this.formData.workMode
			}

			console.log('构建记录数据 - 基础数据:', baseData)

			if (this.formData.workMode === 'tea_picking') {
				// 构建采茶记录数组
				console.log('构建采茶记录 - 原始数据:', this.teaRecords)

				const teaPickingDetails = this.teaRecords
					.filter(record => record.originalWeight && record.price) // 过滤有效记录
					.map(record => ({
						time_period: this.getTimePeriodKey(record.timeIndex),
						original_weight: parseFloat(record.originalWeight),
						project: this.getProjectKey(record.typeIndex),
						price: parseFloat(record.price),
						moisture_rate: parseFloat(record.waterLoss) || 0, // 存储扣水率
						moisture_deduction: parseFloat(record.originalWeight) * (parseFloat(record.waterLoss) || 0) / 100, // 计算扣水量
						actual_weight: parseFloat(record.finalWeight),
						earnings: parseFloat(record.money)
					}))

				console.log('构建采茶记录 - 处理后数据:', teaPickingDetails)
				console.log('构建采茶记录 - 总工钱:', this.totalMoney)

				const teaData = {
					...baseData,
					total_earnings: parseFloat(this.totalMoney),
					tea_picking_details: teaPickingDetails
				}

				console.log('构建采茶记录 - 最终数据:', teaData)
				return teaData
			} else {
				// 时工模式数据构建
				console.log('构建时工记录 - 原始数据:', this.formData.hourly)

				const totalEarnings = parseFloat(this.hourlyTotalEarnings)
				const totalHours = this.hourlyTotalHours
				const averageRate = parseFloat(this.hourlyAverageRate)

				console.log('构建时工记录 - 计算数据:', {
					totalEarnings,
					totalHours,
					averageRate,
					isDetailMode: this.formData.hourly.isDetailMode
				})

				let hourlyDetails = {
					is_detail_mode: this.formData.hourly.isDetailMode,
					total_hours: totalHours,
					total_earnings: totalEarnings,
					average_rate: averageRate
				}

				if (!this.formData.hourly.isDetailMode) {
					// 简化模式
					hourlyDetails.simple_mode = {
						work_hours: parseFloat(this.formData.hourly.simple.workHours) || 0,
						hourly_rate: parseFloat(this.formData.hourly.simple.hourlyRate) || 0
					}
				} else {
					// 详细模式
					hourlyDetails.detail_mode = {
						morning: {
							work_hours: parseFloat(this.formData.hourly.detail.morning.workHours) || 0,
							hourly_rate: parseFloat(this.formData.hourly.detail.morning.hourlyRate) || 0,
							earnings: (parseFloat(this.formData.hourly.detail.morning.workHours) || 0) *
								(parseFloat(this.formData.hourly.detail.morning.hourlyRate) || 0)
						},
						afternoon: {
							work_hours: parseFloat(this.formData.hourly.detail.afternoon.workHours) || 0,
							hourly_rate: parseFloat(this.formData.hourly.detail.afternoon.hourlyRate) || 0,
							earnings: (parseFloat(this.formData.hourly.detail.afternoon.workHours) || 0) *
								(parseFloat(this.formData.hourly.detail.afternoon.hourlyRate) || 0)
						},
						overtime: {
							work_hours: parseFloat(this.formData.hourly.detail.overtime.workHours) || 0,
							hourly_rate: parseFloat(this.formData.hourly.detail.overtime.hourlyRate) || 0,
							earnings: (parseFloat(this.formData.hourly.detail.overtime.workHours) || 0) *
								(parseFloat(this.formData.hourly.detail.overtime.hourlyRate) || 0)
						}
					}
				}

				const hourlyData = {
					...baseData,
					total_earnings: totalEarnings,
					hourly_work_details: hourlyDetails
				}

				console.log('构建时工记录 - 最终数据:', hourlyData)
				return hourlyData
			}
		},

		resetForm() {
			this.formData = {
				workMode: 'tea_picking',
				date: this.todayDate,
				workerName: '',
				// 时工模式字段
				hourly: {
					isDetailMode: false,
					simple: {
						workHours: '',
						hourlyRate: ''
					},
					detail: {
						morning: {
							workHours: '',
							hourlyRate: ''
						},
						afternoon: {
							workHours: '',
							hourlyRate: ''
						},
						overtime: {
							workHours: '',
							hourlyRate: ''
						}
					}
				}
			}

			// 重置采茶记录
			this.teaRecords = [{
				timeIndex: 0,
				originalWeight: '',
				typeIndex: 1, // 默认二叶
				price: '',
				waterLoss: '',
				finalWeight: '0.00',
				money: '0.00'
			}]

			// 重新加载上次保存的单价数据
			this.loadLastPriceData()

			// 重新加载上次保存的表单选择
			this.loadLastFormSelections()
		},

		goBack() {
			uni.navigateBack()
		},

		// 获取按使用频率排序的姓名列表
		getSortedNamesByUsage() {
			if (!this.nameLibrary || this.nameLibrary.length === 0) return []

			// 检查缓存是否有效（5分钟内有效）
			const now = Date.now()
			const cacheValidTime = 5 * 60 * 1000 // 5分钟

			if (now - this.nameUsageCacheTime > cacheValidTime) {
				// 缓存过期，重新计算
				this.updateNameUsageCache()
			}

			// 创建姓名副本并排序
			const namesCopy = [...this.nameLibrary]
			return namesCopy.sort((a, b) => {
				const usageA = this.nameUsageCache[a] || 0
				const usageB = this.nameUsageCache[b] || 0

				// 首先按使用频率降序排序（使用次数多的在前）
				if (usageA !== usageB) {
					return usageB - usageA
				}

				// 使用频率相同时，按字母顺序升序排序
				return a.localeCompare(b)
			})
		},

		// 更新姓名使用频率缓存
		updateNameUsageCache() {
			try {
				const records = uni.getStorageSync('workRecords') || []
				const usageMap = {}

				// 初始化所有姓名的使用次数为0
				this.nameLibrary.forEach(name => {
					usageMap[name] = 0
				})

				// 统计每个姓名的使用次数
				records.forEach(record => {
					if (record.worker_name && usageMap.hasOwnProperty(record.worker_name)) {
						usageMap[record.worker_name]++
					}
				})

				this.nameUsageCache = usageMap
				this.nameUsageCacheTime = Date.now()

				console.log('姓名使用频率缓存已更新:', this.nameUsageCache)
			} catch (error) {
				console.error('更新姓名使用频率缓存失败:', error)
				this.nameUsageCache = {}
				this.nameUsageCacheTime = 0
			}
		},

		// 获取指定姓名的使用次数
		getNameUsage(name) {
			// 检查缓存是否有效
			const now = Date.now()
			const cacheValidTime = 5 * 60 * 1000 // 5分钟

			if (now - this.nameUsageCacheTime > cacheValidTime) {
				this.updateNameUsageCache()
			}

			return this.nameUsageCache[name] || 0
		}
	}
}
</script>

<style scoped>
.add-record-page {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 40rpx;
}

.permission-error {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 60vh;
	padding: 40rpx;
	text-align: center;
}

.error-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
}

.error-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 15rpx;
}

.error-desc {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 40rpx;
}

.back-btn {
	padding: 20rpx 40rpx;
	background-color: #2e7d32;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.form-container {
	padding: 20rpx;
}

.form-section {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	padding-bottom: 15rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.form-item {
	margin-bottom: 30rpx;
	position: relative;
}

.form-item:last-child {
	margin-bottom: 0;
}

.form-label {
	display: block;
	margin-bottom: 15rpx;
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.form-input {
	width: 100%;
	padding: 25rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 36rpx;
	background-color: #fff;
	transition: border-color 0.3s ease;
}

.form-input:focus {
	border-color: #2e7d32;
	outline: none;
}

.form-hint {
	display: block;
	margin-top: 8rpx;
	font-size: 28rpx;
	color: #999;
}

.picker-input {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	background-color: #fff;
	font-size: 36rpx;
	color: #333;
}

.picker-arrow {
	color: #999;
}

.name-input-wrapper {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.name-input-wrapper .form-input {
	flex: 1;
}

.input-icon {
	padding: 25rpx 20rpx;
	background-color: #f0f0f0;
	border-radius: 8rpx;
	font-size: 32rpx;
	cursor: pointer;
}

/* 姓名输入框容器 */
.name-input-wrapper {
	position: relative;
}

/* 姓名建议列表 */
.name-suggestions {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background-color: #fff;
	border: 2rpx solid #e0e0e0;
	border-top: none;
	border-radius: 0 0 8rpx 8rpx;
	max-height: 300rpx;
	overflow-y: auto;
	z-index: 100;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 姓名建议遮罩层 */
.suggestions-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 99;
	background-color: transparent;
}

.suggestion-item {
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.suggestion-item:last-child {
	border-bottom: none;
}

.suggestion-item:hover {
	background-color: #f8f9fa;
}

.radio-group {
	display: flex;
	gap: 15rpx;
	flex-wrap: wrap;
}

.radio-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 15rpx 25rpx;
	background-color: #f8f9fa;
	border: 2rpx solid transparent;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	font-size: 32rpx;
}

.radio-item.active {
	background-color: rgba(76, 175, 80, 0.1);
	border-color: #2e7d32;
	color: #2e7d32;
}

.radio-icon {
	font-size: 28rpx;
}

/* 采茶记录样式 */
.tea-records {
	padding: 0;
}

.tea-record {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	border: 1rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.tea-record:hover {
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	transform: translateY(-2rpx);
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.record-title {
	font-size: 32rpx;
	color: #333333;
	font-weight: 600;
	display: flex;
	align-items: center;
}

.delete-btn {
	font-size: 26rpx;
	color: #f44336;
	padding: 8rpx 16rpx;
	background-color: #ffebee;
	border-radius: 20rpx;
	transition: all 0.3s ease;
	cursor: pointer;
}

.delete-btn:hover {
	background-color: #ffcdd2;
	transform: scale(1.05);
}

.tea-form-item {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	position: relative;
	gap: 16rpx;
}

.tea-label {
	width: 220rpx;
	font-size: 32rpx;
	color: #666666;
	display: flex;
	align-items: center;
	flex-shrink: 0;
	white-space: nowrap;
}

.tea-picker-value {
	flex: 1;
	min-width: 0;
	height: 70rpx;
	line-height: 70rpx;
	background-color: #f8f8f8;
	padding: 0 20rpx;
	border-radius: 8rpx;
	font-size: 32rpx;
	color: #333333;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	cursor: pointer;
}

.tea-picker-value:hover {
	background-color: #f0f0f0;
	border-color: #07c160;
}

.tea-input {
	flex: 1;
	min-width: 0;
	height: 70rpx;
	background-color: #f8f8f8;
	padding: 0 20rpx;
	border-radius: 8rpx;
	font-size: 32rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.tea-input:focus {
	background-color: #ffffff;
	border-color: #07c160;
	box-shadow: 0 0 0 4rpx rgba(7, 193, 96, 0.1);
	outline: none;
}

.tea-unit {
	font-size: 32rpx;
	color: #666666;
	flex-shrink: 0;
}

.tea-value {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	font-weight: 500;
	padding: 0 20rpx;
}

.tea-value.earnings {
	color: #07c160;
	font-weight: 600;
}

/* 只读文本框样式 */
.tea-readonly-input {
	flex: 1;
	min-width: 0;
	height: 70rpx;
	background-color: #f8f8f8;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	transition: all 0.3s ease;
}

.tea-readonly-input.earnings-input {
	background-color: #f0fdf4;
	border-color: #bbf7d0;
}

.tea-readonly-value {
	font-size: 34rpx;
	color: #333333;
	font-weight: 600;
	width: 100%;
	text-align: left;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.tea-readonly-value.earnings {
	color: #07c160;
	font-weight: 700;
	font-size: 36rpx;
}

/* 只读文本框交互效果 */
.tea-readonly-input:hover {
	background-color: #f0f0f0;
	border-color: #d0d0d0;
}

.tea-readonly-input.earnings-input:hover {
	background-color: #ecfdf5;
	border-color: #86efac;
}

/* 汇总信息样式 */
.tea-summary {
	background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-top: 30rpx;
	margin-bottom: 30rpx;
	border: 3rpx solid #1d4ed8;
	box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3), 0 0 0 1rpx rgba(255, 255, 255, 0.1) inset;
	position: relative;
	overflow: hidden;
}

.tea-summary::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.5) 50%, #ffffff 100%);
}

.summary-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
	padding-bottom: 30rpx;
	border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
}

.summary-title-wrapper {
	display: flex;
	align-items: center;
}

.summary-icon-large {
	font-size: 48rpx;
	margin-right: 16rpx;
}

.summary-title {
	font-size: 40rpx;
	color: #ffffff;
	font-weight: 700;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.summary-badge {
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	padding: 8rpx 16rpx;
	backdrop-filter: blur(10rpx);
}

.badge-text {
	font-size: 24rpx;
	color: #ffffff;
	font-weight: 500;
}

.summary-content {
	position: relative;
}

.summary-grid {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.summary-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 16rpx;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.summary-item:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
	background: rgba(255, 255, 255, 1);
}

.summary-item.total-earnings-item {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(239, 246, 255, 0.98) 100%);
	border: 3rpx solid #3b82f6;
	box-shadow: 0 6rpx 16rpx rgba(59, 130, 246, 0.2);
}

.summary-item.total-earnings-item:hover {
	transform: translateY(-6rpx);
	box-shadow: 0 12rpx 28rpx rgba(59, 130, 246, 0.3);
}

.item-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.item-icon-wrapper {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	border: 2rpx solid #dbeafe;
	flex-shrink: 0;
}

.item-icon-wrapper.earnings-icon {
	background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
	border-color: #1d4ed8;
}

.summary-icon {
	font-size: 40rpx;
}

.item-icon-wrapper.earnings-icon .summary-icon {
	color: #ffffff;
	font-weight: 700;
	font-size: 44rpx;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
	filter: none;
}

.item-content {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	flex: 1;
}

.item-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	text-align: right;
	flex-shrink: 0;
}

.summary-label {
	font-size: 28rpx;
	color: #666666;
	font-weight: 500;
	text-align: left;
	width: 100%;
}

.summary-value {
	font-size: 48rpx;
	color: #333333;
	font-weight: 700;
	line-height: 1.2;
	text-align: right;
	font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	letter-spacing: -0.5rpx;
}

.summary-unit {
	font-size: 24rpx;
	color: #999999;
	font-weight: 400;
	text-align: right;
	margin-top: 4rpx;
}

.summary-value.total-earnings {
	color: #1e40af;
	font-size: 56rpx;
	font-weight: 800;
	text-shadow: 0 2rpx 4rpx rgba(30, 64, 175, 0.2);
	text-align: right;
	font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	letter-spacing: -0.8rpx;
}

.summary-unit.earnings-unit {
	color: #1e40af;
	font-weight: 600;
	text-align: right;
}

/* 添加按钮样式 */
.add-record-btn {
	background: linear-gradient(135deg, #33ce7e 30%, #00a854 70%);
	border-radius: 80rpx;
	padding: 20rpx;
	text-align: center;
	margin-top: 20rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

.add-record-btn:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 20rpx rgba(7, 193, 96, 0.4);
	background: linear-gradient(135deg, #00a854 0%, #07c160 100%);
}

.add-record-btn:active {
	transform: translateY(0);
	box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.3);
}

.add-btn-icon {
	font-size: 36rpx;
	color: #ffffff;
	margin-right: 12rpx;
}

.add-btn-text {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: 500;
}

/* 时工模式样式 */
.hourly-section {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	border: 1rpx solid #f0f0f0;
}

.hourly-form-group {
	margin-bottom: 30rpx;
	transition: opacity 0.3s ease;
}

.hourly-form-group.disabled {
	opacity: 0.6;
}

.hourly-form-row {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	gap: 16rpx;
}

.hourly-label {
	width: 120rpx;
	font-size: 32rpx;
	color: #666666;
	display: flex;
	align-items: center;
	flex-shrink: 0;
	white-space: nowrap;
}

.hourly-input-group {
	flex: 1;
	display: flex;
	gap: 16rpx;
}

.hourly-input-wrapper {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.hourly-input {
	flex: 1;
	height: 70rpx;
	background-color: #f8f8f8;
	padding: 0 20rpx;
	border-radius: 8rpx;
	font-size: 32rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.hourly-input:focus {
	background-color: #ffffff;
	border-color: #1e40af;
	box-shadow: 0 0 0 4rpx rgba(30, 64, 175, 0.1);
	outline: none;
}

.hourly-input:disabled {
	background-color: #f5f5f5;
	color: #999999;
	cursor: not-allowed;
}

.hourly-unit {
	font-size: 28rpx;
	color: #666666;
	flex-shrink: 0;
	white-space: nowrap;
}

.expand-btn {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(30, 64, 175, 0.3);
}

.expand-btn:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 20rpx rgba(30, 64, 175, 0.4);
}

.expand-btn:active {
	transform: translateY(0);
	box-shadow: 0 2rpx 8rpx rgba(30, 64, 175, 0.3);
}

.expand-icon {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: bold;
}

.detail-form {
	border-top: 2rpx solid #f0f0f0;
	padding-top: 30rpx;
	margin-top: 20rpx;
}

/* 时工汇总样式 */
.hourly-summary {
	background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-top: 30rpx;
	border: 3rpx solid #1d4ed8;
	box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3), 0 0 0 1rpx rgba(255, 255, 255, 0.1) inset;
	position: relative;
	overflow: hidden;
}

.hourly-summary::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.5) 50%, #ffffff 100%);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
	.tea-form-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 12rpx;
	}

	.tea-label {
		width: 100%;
		margin-bottom: 8rpx;
	}

	.tea-picker-value,
	.tea-input,
	.tea-readonly-input {
		width: 100%;
	}

	.summary-grid {
		gap: 16rpx;
	}

	.summary-item {
		padding: 20rpx;
		flex-direction: column;
		align-items: stretch;
		gap: 12rpx;
	}

	.item-left {
		justify-content: flex-start;
	}

	.item-right {
		align-items: flex-end;
		align-self: flex-end;
		margin-top: 8rpx;
	}

	.item-icon-wrapper {
		width: 60rpx;
		height: 60rpx;
		margin-right: 16rpx;
	}

	.summary-icon {
		font-size: 32rpx;
	}

	.tea-label {
		font-size: 30rpx;
	}

	.tea-picker-value,
	.tea-input {
		font-size: 30rpx;
	}

	.tea-unit {
		font-size: 30rpx;
	}

	.tea-readonly-value {
		font-size: 32rpx;
	}

	.tea-readonly-value.earnings {
		font-size: 34rpx;
	}

	.summary-label {
		font-size: 26rpx;
	}

	.summary-value {
		font-size: 40rpx;
	}

	.summary-value.total-earnings {
		font-size: 44rpx;
	}

	.summary-unit {
		font-size: 22rpx;
	}

	/* 时工响应式 */
	.hourly-form-row {
		flex-direction: column;
		align-items: stretch;
		gap: 12rpx;
	}

	.hourly-label {
		width: 100%;
		margin-bottom: 8rpx;
	}

	.hourly-input-group {
		flex-direction: column;
		gap: 12rpx;
	}

	.hourly-input-wrapper {
		width: 100%;
	}

	.expand-btn {
		align-self: center;
		margin-top: 16rpx;
	}

	.hourly-summary {
		padding: 30rpx;
	}
}



/* 动画效果 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30rpx);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes summarySlideIn {
	from {
		opacity: 0;
		transform: translateY(40rpx) scale(0.95);
	}

	to {
		opacity: 1;
		transform: translateY(0) scale(1);
	}
}

@keyframes summaryPulse {
	0% {
		box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3), 0 0 0 1rpx rgba(255, 255, 255, 0.1) inset;
	}

	50% {
		box-shadow: 0 12rpx 32rpx rgba(59, 130, 246, 0.4), 0 0 0 1rpx rgba(255, 255, 255, 0.2) inset;
	}

	100% {
		box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3), 0 0 0 1rpx rgba(255, 255, 255, 0.1) inset;
	}
}

@keyframes earningsGlow {
	0% {
		text-shadow: 0 2rpx 4rpx rgba(30, 64, 175, 0.2);
	}

	50% {
		text-shadow: 0 2rpx 4rpx rgba(30, 64, 175, 0.4), 0 0 20rpx rgba(30, 64, 175, 0.3);
	}

	100% {
		text-shadow: 0 2rpx 4rpx rgba(30, 64, 175, 0.2);
	}
}

.tea-record {
	animation: fadeInUp 0.3s ease-out;
}

.tea-summary {
	animation: summarySlideIn 0.5s ease-out, summaryPulse 3s ease-in-out infinite;
}

.summary-value.total-earnings {
	animation: earningsGlow 2s ease-in-out infinite;
}

/* 焦点可访问性 */
.tea-input:focus,
.tea-picker-value:focus,
.add-record-btn:focus,
.delete-btn:focus {
	outline: 2rpx solid #07c160;
	outline-offset: 2rpx;
}

/* 禁用状态 */
.tea-input:disabled {
	background-color: #f5f5f5;
	color: #999999;
	cursor: not-allowed;
}

.add-record-btn:disabled {
	background: #cccccc;
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

.add-record-btn:disabled:hover {
	transform: none;
	box-shadow: none;
}

.earnings-display {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	margin-top: 20rpx;
}

.earnings-label {
	font-size: 36rpx;
	color: #333;
	font-weight: 500;
}

.earnings-value {
	font-size: 36rpx;
	color: #2e7d32;
	font-weight: 600;
}

.work-time-display {
	background-color: #f8f9fa;
	border-radius: 8rpx;
	padding: 20rpx;
	margin-top: 20rpx;
}

.time-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.time-item:last-child {
	margin-bottom: 0;
}

.time-label {
	font-size: 32rpx;
	color: #666;
}

.time-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
}

.submit-section {
	padding: 20rpx;
}

.submit-btn {
	width: 100%;
	padding: 30rpx;
	background-color: #2e7d32;
	color: white;
	border: none;
	border-radius: 12rpx;
	font-size: 36rpx;
	font-weight: 600;
	transition: all 0.3s ease;
}

.submit-btn:not(.disabled):active {
	background-color: #45a049;
	transform: scale(0.98);
}

.submit-btn.disabled {
	background-color: #ccc;
	cursor: not-allowed;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.modal-content {
	background-color: #fff;
	border-radius: 12rpx;
	width: 80%;
	max-height: 70vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.modal-close {
	font-size: 36rpx;
	color: #999;
	cursor: pointer;
}

/* 时间段按钮样式 */
.time-period-buttons {
	display: flex;
	gap: 12rpx;
	flex: 1;
}

.time-period-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 10rpx 16rpx;
	background-color: #f8f8f8;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	min-height: 80rpx;
}

.time-period-btn:hover {
	background-color: #f0f0f0;
	border-color: #d0d0d0;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.time-period-btn.active {
	background-color: #e8f5e8;
	border-color: #2e7d32;
	color: #2e7d32;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
}

.time-period-btn.active:hover {
	background-color: #dcedc8;
	border-color: #388e3c;
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 16rpx rgba(76, 175, 80, 0.3);
}

.time-period-btn .btn-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
	display: block;
}

.time-period-btn .btn-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #666;
	display: block;
}

.time-period-btn.active .btn-text {
	color: #2e7d32;
	font-weight: 600;
}

/* 茶叶类型按钮样式 */
.tea-type-buttons {
	display: flex;
	gap: 12rpx;
	flex: 1;
}

.tea-type-btn {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 10rpx 16rpx;
	background-color: #f8f8f8;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	min-height: 80rpx;
}

.tea-type-btn:hover {
	background-color: #f0f0f0;
	border-color: #d0d0d0;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tea-type-btn.active {
	background-color: #e8f5e8;
	border-color: #2e7d32;
	color: #2e7d32;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
}

.tea-type-btn.active:hover {
	background-color: #dcedc8;
	border-color: #388e3c;
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 16rpx rgba(76, 175, 80, 0.3);
}

.tea-type-btn .btn-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
	display: block;
}

.tea-type-btn .btn-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #666;
	display: block;
}

.tea-type-btn.active .btn-text {
	color: #2e7d32;
	font-weight: 600;
}

/* 响应式设计 - 小屏幕适配 */
@media screen and (max-width: 750rpx) {

	.time-period-buttons,
	.tea-type-buttons {
		gap: 8rpx;
	}

	.time-period-btn,
	.tea-type-btn {
		padding: 16rpx 12rpx;
		min-height: 80rpx;
	}

	.time-period-btn .btn-icon,
	.tea-type-btn .btn-icon {
		font-size: 28rpx;
		margin-bottom: 6rpx;
	}

	.time-period-btn .btn-text,
	.tea-type-btn .btn-text {
		font-size: 24rpx;
	}
}

.name-list {
	max-height: 400rpx;
	overflow-y: auto;
}

.name-item {
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.name-item:last-child {
	border-bottom: none;
}

.name-item:active {
	background-color: #f8f9fa;
}

.name-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.name-text {
	font-size: 32rpx;
	color: #333;
	flex: 1;
}

.usage-count {
	font-size: 24rpx;
	color: #999;
	background-color: #f0f0f0;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	margin-left: 16rpx;
}

.empty-state {
	padding: 60rpx;
	text-align: center;
	color: #999;
	font-size: 28rpx;
}
</style>
