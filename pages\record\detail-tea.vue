<template>
	<view class="detail-page" :class="{ 'monthly-theme': source === 'monthly' }">
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<text class="nav-icon">←</text>
				<text class="nav-text">返回</text>
			</view>
			<view class="nav-title">采茶记录详情</view>
			<view class="nav-right">
				<!-- 查看模式：显示编辑按钮 -->
				<text class="edit-btn" v-if="isAdmin && recordFound && !isEditing"
					@click="enterEditMode">编辑</text>

				<!-- 编辑模式：显示保存和取消按钮 -->
				<view v-if="isEditing" class="edit-actions">
					<text class="save-btn" @click="saveChanges" :class="{ disabled: saving }">
						{{ saving ? '保存中...' : '保存' }}
					</text>
					<text class="cancel-btn" @click="cancelEdit">取消</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 记录未找到 -->
		<view class="error-container" v-else-if="!recordFound">
			<text class="error-icon">❌</text>
			<text class="error-text">记录不存在或已被删除</text>
			<button class="back-btn" @click="goBack">返回列表</button>
		</view>

		<!-- 记录详情 -->
		<view class="detail-content" v-else>
			<!-- 基本信息卡片 -->
			<view class="info-card">
				<view class="card-header">
					<text class="card-title">📋 基本信息</text>
					<view class="work-mode-badge tea-picking">
						<text>🍃 采茶</text>
					</view>
				</view>
				<view class="info-grid">
					<view class="info-item">
						<text class="info-label">工人姓名</text>
						<!-- 查看模式 -->
						<text v-if="!isEditing" class="info-value">{{ recordData.worker_name }}</text>
						<!-- 编辑模式 -->
						<input v-else v-model="editData.worker_name" class="edit-input" placeholder="请输入工人姓名"
							maxlength="20" />
					</view>
					<view class="info-item">
						<text class="info-label">工作日期</text>
						<!-- 查看模式 -->
						<text v-if="!isEditing" class="info-value">{{ $formatDate(recordData.date) }}</text>
						<!-- 编辑模式 -->
						<picker v-else mode="date" :value="editData.date" @change="onDateChange" class="edit-picker">
							<view class="picker-text">{{ editData.date || '请选择日期' }}</view>
						</picker>
					</view>
					<view class="info-item">
						<text class="info-label">总工钱</text>
						<text class="info-value earnings">¥{{ $formatCurrency(calculateTotalEarnings()) }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">记录时间</text>
						<text class="info-value">{{ $formatDate(recordData.created_at, 'YYYY-MM-DD HH:mm') }}</text>
					</view>
				</view>
			</view>

			<!-- 统计汇总卡片 -->
			<view class="summary-card">
				<view class="card-header">
					<text class="card-title">📊 统计汇总</text>
				</view>
				<view class="summary-grid">
					<view class="summary-item">
						<text class="summary-label">项目类型</text>
						<text class="summary-value">{{ getProjectSummary() }}</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">总原斤数</text>
						<text class="summary-value">{{ getTotalOriginalWeight() }}斤</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">总扣水量</text>
						<text class="summary-value">{{ getTotalMoistureDeduction() }}斤</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">总实际斤数</text>
						<text class="summary-value">{{ getTotalActualWeight() }}斤</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">平均单价</text>
						<text class="summary-value">¥{{ getAveragePrice() }}/斤</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">总工钱</text>
						<text class="summary-value earnings">¥{{ $formatCurrency(recordData.total_earnings) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 采茶详情卡片 -->
		<view class="detail-card">
			<view class="card-header">
				<text class="card-title">🍃 采茶详情</text>
				<view class="header-actions">
					<text class="detail-count">共{{ getCurrentDetails().length }}条记录</text>
					<text v-if="isEditing" class="add-detail-btn" @click="addDetailRecord">+ 添加</text>
				</view>
			</view>

			<view class="detail-list">
				<view class="detail-item"
					:class="{
						editing: isEditing,
						merged: detail.is_merged
					}"
					v-for="(detail, index) in getCurrentDetails()" :key="index">
					<view class="detail-header">
						<!-- 查看模式：显示时间段和合并信息 -->
						<view v-if="!isEditing" class="detail-header-view">
							<text class="detail-time">{{ getTimePeriodText(detail.time_period) }}</text>
							<text v-if="detail.is_merged" class="merged-info">
								（合并{{ detail.merged_count }}条记录）
							</text>
						</view>
						<!-- 编辑模式：禁用编辑合并后的条目 -->
						<view v-else class="edit-header-actions">
							<text class="detail-time readonly" v-if="detail.is_merged">
								{{ getTimePeriodText(detail.time_period) }}（合并条目，不可编辑）
							</text>
							<view v-else class="edit-controls">
								<picker :range="timeOptions" :value="getTimeIndex(detail.time_period)"
									@change="onTimePeriodChange(index, $event)" class="time-picker">
									<view class="picker-text">{{ getTimePeriodText(detail.time_period) }}</view>
								</picker>
								<text class="delete-detail-btn" @click="removeDetailRecord(index)"
									v-if="getCurrentDetails().length > 1">删除</text>
							</view>
						</view>
					</view>
					<view class="detail-grid">
						<view class="detail-field">
							<text class="field-label">项目类型</text>
							<!-- 查看模式 -->
							<text v-if="!isEditing" class="field-value">{{ getDetailProjectText(detail) }}</text>
							<!-- 编辑模式 -->
							<picker v-else-if="!detail.is_merged" :range="projectOptions" :value="getProjectIndex(detail.project)"
								@change="onProjectChange(index, $event)" class="edit-picker">
								<view class="picker-text">{{ getProjectText(detail.project) }}</view>
							</picker>
							<!-- 合并条目在编辑模式下只读 -->
							<text v-else class="field-value readonly">{{ getDetailProjectText(detail) }}</text>
						</view>
						<view class="detail-field">
							<text class="field-label">原斤数</text>
							<!-- 查看模式 -->
							<text v-if="!isEditing" class="field-value">{{ detail.original_weight }}斤</text>
							<!-- 编辑模式：合并条目只读 -->
							<input v-else-if="!detail.is_merged" v-model="detail.original_weight" class="edit-input number-input" type="digit"
								placeholder="0.00" @input="onWeightChange(index)" />
							<text v-else class="field-value readonly">{{ detail.original_weight }}斤</text>
						</view>
						<view class="detail-field">
							<text class="field-label">扣水量</text>
							<!-- 查看模式 -->
							<text v-if="!isEditing" class="field-value">{{ formatMoistureDeduction(detail) }}</text>
							<!-- 编辑模式：合并条目只读 -->
							<view v-else-if="!detail.is_merged" class="moisture-edit-container">
								<input v-model="detail.moisture_rate"
									class="edit-input number-input moisture-rate-input" type="digit" placeholder="0"
									@input="onMoistureRateChange(index)" />
								<text class="moisture-unit">%（{{ calculateMoistureAmount(detail) }}斤）</text>
							</view>
							<text v-else class="field-value readonly">{{ formatMoistureDeduction(detail) }}</text>
						</view>
						<view class="detail-field">
							<text class="field-label">实际斤数</text>
							<text class="field-value">{{ detail.actual_weight }}斤</text>
						</view>
						<view class="detail-field">
							<text class="field-label">单价</text>
							<!-- 查看模式 -->
							<text v-if="!isEditing" class="field-value">¥{{ $formatCurrency(detail.price) }}/斤</text>
							<!-- 编辑模式：合并条目只读 -->
							<input v-else-if="!detail.is_merged" v-model="detail.price" class="edit-input number-input" type="digit"
								placeholder="0.00" @input="onPriceChange(index)" />
							<text v-else class="field-value readonly">¥{{ $formatCurrency(detail.price) }}/斤</text>
						</view>
						<view class="detail-field">
							<text class="field-label">工钱</text>
							<text class="field-value earnings">¥{{ $formatCurrency(calculateEarnings(detail)) }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'TeaRecordDetailPage',
	data() {
		return {
			recordId: null,
			loading: true,
			recordFound: false,
			recordData: {},
			teaDetails: [],
			// 来源标识
			source: 'daily', // 'daily' 或 'monthly'
			// 编辑状态
			isEditing: false,
			editData: {
				worker_name: '',
				date: '',
				tea_picking_details: []
			},
			originalData: {},
			// 表单选项
			timeOptions: ['上午', '下午'],
			projectOptions: ['一叶', '二叶', '三叶'],
			// 保存状态
			saving: false
		}
	},
	computed: {
		...mapGetters('user', ['isAdmin'])
	},
	onLoad(options) {
		// 初始化用户状态
		this.$store.dispatch('user/initUserState')

		// 适配状态栏高度
		this.adaptStatusBar()

		// 设置来源
		if (options.source) {
			this.source = options.source
		}

		if (options.id) {
			this.recordId = options.id
			this.loadRecord()
		} else {
			this.loading = false
			this.recordFound = false
		}
	},
	methods: {
		// 适配状态栏高度
		adaptStatusBar() {
			try {
				const systemInfo = uni.getSystemInfoSync()
				const statusBarHeight = systemInfo.statusBarHeight || 0

				console.log('状态栏高度:', statusBarHeight)

				// 设置 CSS 变量作为备用方案
				if (statusBarHeight > 0) {
					// #ifdef H5
					if (typeof document !== 'undefined') {
						document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight}px`)

						// 检查是否支持 safe-area-inset
						const supportsSafeArea = CSS.supports('padding-top', 'env(safe-area-inset-top)') ||
							CSS.supports('padding-top', 'constant(safe-area-inset-top)')

						if (!supportsSafeArea) {
							// 使用 JavaScript 动态设置
							this.$nextTick(() => {
								const detailPage = document.querySelector('.detail-page')
								if (detailPage) {
									detailPage.style.paddingTop = `${statusBarHeight}px`
								}

								const navBar = document.querySelector('.nav-bar')
								if (navBar) {
									navBar.style.marginTop = `-${statusBarHeight}px`
									navBar.style.paddingTop = `${statusBarHeight + 10}px` // 10px 对应原来的 20rpx
								}
							})
						}
					}
					// #endif

					// #ifdef APP-PLUS || MP
					// 在 App 和小程序中，通过设置页面样式
					this.$nextTick(() => {
						const pages = getCurrentPages()
						const currentPage = pages[pages.length - 1]
						if (currentPage && currentPage.$vm) {
							currentPage.$vm.$el.style.paddingTop = `${statusBarHeight}px`
						}
					})
					// #endif
				}
			} catch (error) {
				console.log('状态栏适配失败:', error)
			}
		},

		async loadRecord() {
			try {
				this.loading = true
				console.log('开始加载记录, recordId:', this.recordId)

				const records = uni.getStorageSync('workRecords') || []
				console.log('本地记录总数:', records.length)

				const record = records.find(r => r.id == this.recordId)
				console.log('找到的记录:', record)

				if (record && record.work_mode === 'tea_picking') {
					this.recordFound = true
					this.recordData = record
					this.teaDetails = this.getTeaDetailsArray(record)
					console.log('记录加载成功, recordFound:', this.recordFound)
				} else {
					this.recordFound = false
					console.log('记录未找到或类型不匹配')
				}

				// 检查用户状态
				console.log('当前用户状态:', this.$store.state.user)
				console.log('isAdmin状态:', this.isAdmin)
			} catch (error) {
				console.error('加载记录失败:', error)
				this.recordFound = false
				uni.showToast({
					title: '加载失败',
					icon: 'error'
				})
			} finally {
				this.loading = false
				console.log('记录加载完成, loading:', this.loading, 'recordFound:', this.recordFound)
			}
		},

		// 获取采茶详情数组（复用列表页面的逻辑）
		getTeaDetailsArray(record) {
			if (!record.tea_picking_details) {
				return []
			}

			if (Array.isArray(record.tea_picking_details)) {
				return record.tea_picking_details
			}

			if (typeof record.tea_picking_details === 'object') {
				const keys = Object.keys(record.tea_picking_details)
				const numericKeys = keys.filter(key => /^\d+$/.test(key)).sort((a, b) => parseInt(a) - parseInt(b))

				if (numericKeys.length > 0) {
					return numericKeys.map(key => record.tea_picking_details[key])
				}

				if (record.tea_picking_details.project || record.tea_picking_details.original_weight) {
					return [record.tea_picking_details]
				}
			}

			return []
		},

		// 统计方法
		getProjectSummary() {
			const projectCounts = {}
			this.teaDetails.forEach(detail => {
				const project = this.getProjectText(detail.project)
				projectCounts[project] = (projectCounts[project] || 0) + 1
			})

			return Object.entries(projectCounts)
				.map(([project, count]) => count > 1 ? `${project}×${count}` : project)
				.join('、') || '未知项目'
		},

		getTotalOriginalWeight() {
			return this.teaDetails.reduce((sum, detail) => {
				return sum + (parseFloat(detail.original_weight) || 0)
			}, 0).toFixed(2)
		},

		getTotalMoistureDeduction() {
			return this.teaDetails.reduce((sum, detail) => {
				const originalWeight = parseFloat(detail.original_weight) || 0
				const moistureRate = parseFloat(detail.moisture_rate) || 0
				return sum + (originalWeight * moistureRate / 100) // 基于扣水率计算
			}, 0).toFixed(2)
		},

		getTotalActualWeight() {
			return this.teaDetails.reduce((sum, detail) => {
				return sum + (parseFloat(detail.actual_weight) || 0)
			}, 0).toFixed(2)
		},

		getAveragePrice() {
			const totalActualWeight = parseFloat(this.getTotalActualWeight())
			if (totalActualWeight === 0) return '0.00'

			return (parseFloat(this.recordData.total_earnings) / totalActualWeight).toFixed(2)
		},

		// 表单处理方法
		getCurrentDetails() {
			if (this.isEditing) {
				// 编辑模式：返回原始未合并的数据
				return this.editData.tea_picking_details
			} else {
				// 查看模式：返回合并后的数据
				return this.mergeDetailsByTimePeriod(this.teaDetails)
			}
		},

		// 按时间段合并条目
		mergeDetailsByTimePeriod(details) {
			if (!details || details.length === 0) {
				return []
			}

			// 按时间段分组
			const groupedDetails = {
				morning: [],
				afternoon: []
			}

			details.forEach(detail => {
				let timePeriod = detail.time_period

				// 将中午时间段映射到下午
				if (timePeriod === 'noon') {
					timePeriod = 'afternoon'
				}

				// 只处理上午和下午时间段
				if (timePeriod === 'morning' || timePeriod === 'afternoon') {
					groupedDetails[timePeriod].push(detail)
				}
			})

			const mergedDetails = []

			// 合并上午条目
			if (groupedDetails.morning.length > 0) {
				const morningMerged = this.mergeDetailsGroup(groupedDetails.morning, 'morning')
				mergedDetails.push(morningMerged)
			}

			// 合并下午条目
			if (groupedDetails.afternoon.length > 0) {
				const afternoonMerged = this.mergeDetailsGroup(groupedDetails.afternoon, 'afternoon')
				mergedDetails.push(afternoonMerged)
			}

			return mergedDetails
		},

		// 合并同一时间段的条目组
		mergeDetailsGroup(detailsGroup, timePeriod) {
			if (detailsGroup.length === 1) {
				// 只有一个条目，直接返回
				return { ...detailsGroup[0], time_period: timePeriod }
			}

			// 多个条目需要合并
			let totalOriginalWeight = 0
			let totalActualWeight = 0
			let totalEarnings = 0
			let totalMoistureDeduction = 0
			let weightedMoistureRate = 0
			let weightedPrice = 0

			// 收集所有项目类型
			const projects = new Set()

			detailsGroup.forEach(detail => {
				const originalWeight = parseFloat(detail.original_weight) || 0
				const actualWeight = parseFloat(detail.actual_weight) || 0
				const earnings = parseFloat(detail.earnings) || 0
				const moistureRate = parseFloat(detail.moisture_rate) || 0
				const price = parseFloat(detail.price) || 0

				totalOriginalWeight += originalWeight
				totalActualWeight += actualWeight
				totalEarnings += earnings
				totalMoistureDeduction += (originalWeight * moistureRate / 100)

				// 加权计算平均扣水率和价格
				weightedMoistureRate += (moistureRate * originalWeight)
				weightedPrice += (price * actualWeight)

				projects.add(detail.project)
			})

			// 计算平均值
			const avgMoistureRate = totalOriginalWeight > 0 ? (weightedMoistureRate / totalOriginalWeight) : 0
			const avgPrice = totalActualWeight > 0 ? (weightedPrice / totalActualWeight) : 0

			// 确定主要项目类型（取第一个）
			const mainProject = detailsGroup[0].project

			return {
				time_period: timePeriod,
				original_weight: totalOriginalWeight.toFixed(2),
				actual_weight: totalActualWeight.toFixed(2),
				earnings: totalEarnings.toFixed(2),
				moisture_rate: avgMoistureRate.toFixed(1),
				moisture_deduction: totalMoistureDeduction.toFixed(2),
				price: avgPrice.toFixed(2),
				project: mainProject,
				// 标记为合并条目
				is_merged: detailsGroup.length > 1,
				merged_count: detailsGroup.length,
				merged_projects: Array.from(projects)
			}
		},

		onDateChange(e) {
			this.editData.date = e.detail.value
		},

		onTimePeriodChange(index, e) {
			const timeKeys = ['morning', 'afternoon']
			this.editData.tea_picking_details[index].time_period = timeKeys[e.detail.value]
		},

		onProjectChange(index, e) {
			const projectKeys = ['one', 'two', 'three']
			this.editData.tea_picking_details[index].project = projectKeys[e.detail.value]
		},

		onWeightChange(index) {
			const detail = this.editData.tea_picking_details[index]
			// 基于扣水率自动计算扣水量和实际斤数
			const originalWeight = parseFloat(detail.original_weight) || 0
			const moistureRate = parseFloat(detail.moisture_rate) || 0
			const moistureDeduction = originalWeight * moistureRate / 100
			detail.moisture_deduction = moistureDeduction.toFixed(2)
			detail.actual_weight = Math.max(0, originalWeight - moistureDeduction).toFixed(2)
		},

		// 扣水率变化时重新计算
		onMoistureRateChange(index) {
			this.onWeightChange(index) // 复用重量变化的计算逻辑
		},

		onPriceChange() {
			// 价格变化时会自动重新计算工钱（通过计算属性）
		},

		addDetailRecord() {
			this.editData.tea_picking_details.push({
				time_period: 'morning',
				original_weight: '',
				project: 'one',
				price: '',
				moisture_rate: 0, // 扣水率，默认0%
				moisture_deduction: 0,
				actual_weight: 0,
				earnings: 0
			})
		},

		removeDetailRecord(index) {
			if (this.editData.tea_picking_details.length > 1) {
				this.editData.tea_picking_details.splice(index, 1)
			}
		},

		// 计算方法
		calculateActualWeight(detail) {
			const originalWeight = parseFloat(detail.original_weight) || 0
			const moistureRate = parseFloat(detail.moisture_rate) || 0
			const moistureDeduction = originalWeight * moistureRate / 100 // 基于扣水率计算
			return Math.max(0, originalWeight - moistureDeduction).toFixed(2)
		},

		calculateEarnings(detail) {
			const actualWeight = parseFloat(this.calculateActualWeight(detail))
			const price = parseFloat(detail.price) || 0
			return (actualWeight * price).toFixed(2)
		},

		// 计算扣水量（基于扣水率）
		calculateMoistureAmount(detail) {
			const originalWeight = parseFloat(detail.original_weight) || 0
			const moistureRate = parseFloat(detail.moisture_rate) || 0
			return (originalWeight * moistureRate / 100).toFixed(2)
		},

		// 格式化扣水量显示
		formatMoistureDeduction(detail) {
			const originalWeight = parseFloat(detail.original_weight) || 0
			const moistureRate = parseFloat(detail.moisture_rate) || 0 // 从数据中获取扣水率
			const moistureAmount = (originalWeight * moistureRate / 100).toFixed(2)
			return `${moistureAmount}斤（${moistureRate}%）`
		},

		calculateTotalEarnings() {
			if (!this.isEditing) {
				return this.recordData.total_earnings
			}

			return this.editData.tea_picking_details.reduce((total, detail) => {
				return total + parseFloat(this.calculateEarnings(detail))
			}, 0).toFixed(2)
		},

		// 辅助方法
		getTimePeriodText(period) {
			return period === 'morning' ? '上午' : '下午'
		},

		getProjectText(project) {
			const projectMap = { one: '一叶', two: '二叶', three: '三叶' }
			return projectMap[project] || project
		},

		// 获取详情项目文本（处理合并条目）
		getDetailProjectText(detail) {
			if (detail.is_merged && detail.merged_projects && detail.merged_projects.length > 1) {
				// 合并条目显示所有项目类型
				return detail.merged_projects.map(project => this.getProjectText(project)).join('、')
			} else {
				// 单个条目显示单个项目类型
				return this.getProjectText(detail.project)
			}
		},

		getTimeIndex(period) {
			return period === 'morning' ? 0 : 1
		},

		getProjectIndex(project) {
			const projectKeys = ['one', 'two', 'three']
			return projectKeys.indexOf(project)
		},



		// 编辑模式相关方法
		enterEditMode() {
			this.isEditing = true
			// 深拷贝原始数据用于取消操作
			this.originalData = JSON.parse(JSON.stringify(this.recordData))
			// 初始化编辑数据，使用原始未合并的数据
			this.editData = {
				worker_name: this.recordData.worker_name,
				date: this.recordData.date,
				tea_picking_details: JSON.parse(JSON.stringify(this.teaDetails))
			}
		},

		// 获取原始未合并的详情数据用于编辑
		getRawDetailsForEdit() {
			return this.isEditing ? this.editData.tea_picking_details : this.teaDetails
		},

		cancelEdit() {
			this.isEditing = false
			// 恢复原始数据
			this.recordData = JSON.parse(JSON.stringify(this.originalData))
			this.teaDetails = this.getTeaDetailsArray(this.recordData)
			this.editData = {}
			this.originalData = {}
		},

		async saveChanges() {
			if (this.saving) return

			try {
				// 验证数据
				if (!this.validateEditData()) {
					return
				}

				this.saving = true

				// 计算总工钱
				const totalEarnings = this.calculateTotalEarnings()

				// 构建更新后的记录数据
				const updatedRecord = {
					...this.recordData,
					worker_name: this.editData.worker_name,
					date: this.editData.date,
					total_earnings: totalEarnings,
					tea_picking_details: this.editData.tea_picking_details,
					updated_at: new Date().toISOString()
				}

				// 更新本地存储
				const records = uni.getStorageSync('workRecords') || []
				const recordIndex = records.findIndex(r => r.id == this.recordId)

				if (recordIndex !== -1) {
					// 检测产量是否发生变化
					const originalProduction = this.calculateTotalProduction(this.originalData.tea_picking_details || [])
					const newProduction = this.calculateTotalProduction(updatedRecord.tea_picking_details || [])
					const productionChanged = Math.abs(originalProduction - newProduction) > 0.01

					records[recordIndex] = updatedRecord
					uni.setStorageSync('workRecords', records)

					// 更新页面数据
					this.recordData = updatedRecord
					this.teaDetails = this.editData.tea_picking_details

					// 退出编辑模式
					this.isEditing = false
					this.editData = {}
					this.originalData = {}

					// 触发收入页面数据更新事件
					console.log('📡 [采茶记录详情] 工作记录修改完成，发送updateIncomeRecord事件')
					console.log('📡 [采茶记录详情] 修改的记录:', {
						id: updatedRecord.id,
						date: updatedRecord.date,
						worker_name: updatedRecord.worker_name,
						total_earnings: updatedRecord.total_earnings,
						productionChanged: productionChanged,
						originalProduction: originalProduction,
						newProduction: newProduction
					})

					// 添加延迟确保事件监听器已注册
					setTimeout(() => {
						uni.$emit('updateIncomeRecord', {
							type: 'workRecordUpdated',
							updatedRecord: updatedRecord,
							productionChanged: productionChanged,
							originalProduction: originalProduction,
							newProduction: newProduction,
							timestamp: new Date().toISOString()
						})

						console.log('📡 [采茶记录详情] updateIncomeRecord事件已发送（延迟发送）')
					}, 100)

					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
				} else {
					throw new Error('记录不存在')
				}
			} catch (error) {
				console.error('保存失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'error'
				})
			} finally {
				this.saving = false
			}
		},

		// 数据验证
		validateEditData() {
			if (!this.editData.worker_name.trim()) {
				uni.showToast({
					title: '请输入工人姓名',
					icon: 'error'
				})
				return false
			}

			if (!this.editData.date) {
				uni.showToast({
					title: '请选择工作日期',
					icon: 'error'
				})
				return false
			}

			// 验证采茶详情
			for (let i = 0; i < this.editData.tea_picking_details.length; i++) {
				const detail = this.editData.tea_picking_details[i]

				if (!detail.original_weight || parseFloat(detail.original_weight) <= 0) {
					uni.showToast({
						title: `第${i + 1}条记录的原斤数无效`,
						icon: 'error'
					})
					return false
				}

				if (!detail.price || parseFloat(detail.price) <= 0) {
					uni.showToast({
						title: `第${i + 1}条记录的单价无效`,
						icon: 'error'
					})
					return false
				}
			}

			return true
		},

		// 操作方法
		goBack() {
			if (this.isEditing) {
				uni.showModal({
					title: '提示',
					content: '您有未保存的更改，确定要离开吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack()
						}
					}
				})
			} else {
				uni.navigateBack()
			}
		},

		// 计算采茶记录的总产量
		calculateTotalProduction(teaDetails) {
			if (!Array.isArray(teaDetails)) {
				return 0
			}
			return teaDetails.reduce((total, detail) => {
				const actualWeight = parseFloat(detail.actual_weight || detail.finalWeight || 0)
				return total + actualWeight
			}, 0)
		},
	}
}
</script>

<style scoped>
.detail-page {
	min-height: 100vh;
	background-color: #f5f5f5;
	/* 适配状态栏高度 - 多种兼容方案 */
	padding-top: constant(safe-area-inset-top);
	/* iOS 11.0-11.2 */
	padding-top: env(safe-area-inset-top);
	/* iOS 11.2+ */
	/* 备用方案 */
	padding-top: var(--status-bar-height, 0px);
}

/* 导航栏样式 - 采茶模式绿色主题 */
.nav-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: linear-gradient(135deg, #2e7d32 0%, #2E7D32 100%);
	border-bottom: 2rpx solid rgba(76, 175, 80, 0.3);
	position: sticky;
	top: 0;
	z-index: 100;
	box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.2);
	/* 确保导航栏不会被状态栏遮挡 - 多种兼容方案 */
	margin-top: calc(-1 * constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	margin-top: calc(-1 * env(safe-area-inset-top));
	/* iOS 11.2+ */
	margin-top: calc(-1 * var(--status-bar-height, 0px));
	/* 备用方案 */

	padding-top: calc(20rpx + constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	padding-top: calc(20rpx + env(safe-area-inset-top));
	/* iOS 11.2+ */
	padding-top: calc(20rpx + var(--status-bar-height, 0px));
	/* 备用方案 */
}

.nav-left {
	display: flex;
	align-items: center;
	gap: 10rpx;
	cursor: pointer;
}

.nav-icon {
	font-size: 36rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.nav-text {
	font-size: 32rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.nav-right {
	min-width: 100rpx;
	text-align: right;
}

.edit-btn {
	padding: 12rpx 24rpx;
	background-color: rgba(255, 255, 255, 0.2);
	color: white;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 20rpx;
	font-size: 28rpx;
	cursor: pointer;
	backdrop-filter: blur(10rpx);
}

.edit-btn:active {
	background-color: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

/* 编辑模式按钮组 */
.edit-actions {
	display: flex;
	gap: 15rpx;
}

.save-btn,
.cancel-btn {
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.save-btn {
	background-color: #f24251;
	color: white;
}

.save-btn.disabled {
	background-color: #ccc;
	cursor: not-allowed;
}

.save-btn:not(.disabled):active {
	background-color: #e11919;
}

.cancel-btn {
	background-color: #2545d4;
	color: white;
}

.cancel-btn:active {
	background-color: #423d3d;
}

/* 编辑表单样式 */
.edit-input {
	padding: 16rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 32rpx;
	color: #333;
	background-color: #fff;
	transition: all 0.3s ease;
	box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.edit-input:focus {
	border-color: #2e7d32;
	outline: none;
	box-shadow: 0 2rpx 8rpx rgba(46, 125, 50, 0.15);
	background-color: #fafafa;
}

/* 编辑输入框悬停效果 */
.edit-input:hover {
	border-color: #66bb6a;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.number-input {
	text-align: right;
}

.edit-picker {
	padding: 16rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	background-color: #fff;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.edit-picker:hover {
	border-color: #66bb6a;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.edit-picker:active {
	border-color: #2e7d32;
	box-shadow: 0 2rpx 8rpx rgba(46, 125, 50, 0.15);
}

.picker-text {
	font-size: 32rpx;
	color: #333;
}

.time-picker {
	padding: 8rpx 16rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 6rpx;
	background-color: #fff;
	font-size: 26rpx;
}

/* 详情卡片头部 */
.header-actions {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.add-detail-btn {
	padding: 8rpx 16rpx;
	background-color: #2e7d32;
	color: white;
	border-radius: 16rpx;
	font-size: 24rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 6rpx rgba(46, 125, 50, 0.3);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.add-detail-btn:hover {
	background-color: #388e3c;
	transform: translateY(-1rpx);
	box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.4);
}

.add-detail-btn:active {
	background-color: #1b5e20;
	transform: translateY(0);
	box-shadow: 0 2rpx 6rpx rgba(46, 125, 50, 0.3);
}

/* 详情项头部编辑 */
.edit-header-actions {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.delete-detail-btn {
	padding: 6rpx 12rpx;
	background-color: #f44336;
	color: white;
	border-radius: 12rpx;
	font-size: 22rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 4rpx rgba(244, 67, 54, 0.3);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.delete-detail-btn:hover {
	background-color: #e53935;
	transform: translateY(-1rpx);
	box-shadow: 0 3rpx 8rpx rgba(244, 67, 54, 0.4);
}

.delete-detail-btn:active {
	background-color: #d32f2f;
	transform: translateY(0);
	box-shadow: 0 2rpx 4rpx rgba(244, 67, 54, 0.3);
}

/* 加载和错误状态 */
.loading-container,
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	text-align: center;
}

.loading-text {
	font-size: 32rpx;
	color: #666;
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.error-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 40rpx;
}

.back-btn {
	padding: 20rpx 40rpx;
	background-color: #2e7d32;
	color: white;
	border: none;
	border-radius: 25rpx;
	font-size: 32rpx;
}

/* 详情内容 */
.detail-content {
	padding: 20rpx;
}

/* 卡片通用样式 */
.info-card,
.detail-card,
.summary-card {
	background-color: #fff;
	border-radius: 15rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	background: linear-gradient(135deg, #2e7d32 0%, #45a049 100%);
	color: white;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
}

.work-mode-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	background-color: rgba(255, 255, 255, 0.2);
}

.detail-count {
	font-size: 26rpx;
	opacity: 0.9;
}

/* 基本信息网格 */
.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30rpx;
	padding: 30rpx;
}

.info-item {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.info-label {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
}

.info-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

.info-value.earnings {
	color: #2e7d32;
	font-size: 36rpx;
}

/* 详情列表 */
.detail-list {
	padding: 0 30rpx 30rpx;
}

.detail-item {
	border: 2rpx solid #f0f0f0;
	border-radius: 12rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	background-color: #ffffff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;
	position: relative;
}

/* 编辑模式下增强视觉分界 */
.detail-item.editing {
	border: 2rpx solid #e3f2fd;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 16rpx rgba(46, 125, 50, 0.1);
	background-color: #fafafa;
}

/* 合并条目特殊样式 */
.detail-item.merged {
	border: 2rpx solid #e8f5e8;
	background: linear-gradient(135deg, #f1f8e9 0%, #ffffff 100%);
	box-shadow: 0 3rpx 12rpx rgba(76, 175, 80, 0.15);
}

/* 编辑模式下的合并条目 */
.detail-item.merged.editing {
	border: 2rpx solid #c8e6c9;
	background: linear-gradient(135deg, #e8f5e8 0%, #f5f5f5 100%);
	box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.2);
}

.detail-item:last-child {
	margin-bottom: 0;
}

/* 条目间分隔线 */
.detail-item:not(:last-child)::after {
	content: '';
	position: absolute;
	bottom: -20rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 80%;
	height: 2rpx;
	background: linear-gradient(90deg, transparent 0%, #e0e0e0 50%, transparent 100%);
	opacity: 0.6;
}

/* 编辑模式下的分隔线 */
.detail-item.editing:not(:last-child)::after {
	bottom: -25rpx;
	background: linear-gradient(90deg, transparent 0%, #2e7d32 50%, transparent 100%);
	opacity: 0.3;
	height: 3rpx;
}

.detail-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 24rpx;
	background-color: #f8f9fa;
	border-bottom: 2rpx solid #f0f0f0;
	position: relative;
}

/* 编辑模式下的头部样式 */
.editing .detail-header {
	background: linear-gradient(135deg, #eac376 0%, #f8f9fa 100%);
	border-bottom: 2rpx solid #bbdefb;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

/* 合并条目的头部样式 */
.merged .detail-header {
	background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
	border-bottom: 2rpx solid #c8e6c9;
}

/* 编辑模式下合并条目的头部样式 */
.merged.editing .detail-header {
	background: linear-gradient(135deg, #dcedc8 0%, #e8f5e8 100%);
	border-bottom: 2rpx solid #aed581;
}

.detail-index {
	font-size: 28rpx;
	font-weight: 600;
	color: #2e7d32;
}

.detail-time {
	font-size: 26rpx;
	color: #666;
	padding: 6rpx 12rpx;
	background-color: #e8f5e8;
	border-radius: 12rpx;
}

.detail-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
	padding: 24rpx;
	background-color: #ffffff;
	transition: background-color 0.3s ease;
}

/* 编辑模式下的网格样式 */
.editing .detail-grid {
	background-color: #fafafa;
	border-top: 1rpx solid #e0e0e0;
	padding: 28rpx 24rpx;
}

/* 合并条目的网格样式 */
.merged .detail-grid {
	background: linear-gradient(135deg, #ffffff 0%, #f9fbe7 100%);
}

/* 编辑模式下合并条目的网格样式 */
.merged.editing .detail-grid {
	background: linear-gradient(135deg, #f5f5f5 0%, #f1f8e9 100%);
}

.detail-field {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.field-label {
	font-size: 24rpx;
	color: #666;
}

.field-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.field-value.earnings {
	color: #2e7d32;
	font-weight: 600;
}

/* 统计汇总网格 */
.summary-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30rpx;
	padding: 30rpx;
}

.summary-item {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	text-align: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.summary-label {
	font-size: 26rpx;
	color: #666;
}

.summary-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

.summary-value.earnings {
	color: #2e7d32;
	font-size: 36rpx;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {

	.info-grid,
	.detail-grid,
	.summary-grid {
		grid-template-columns: 1fr;
		gap: 20rpx;
	}

	.nav-title {
		font-size: 32rpx;
	}

	.edit-btn {
		font-size: 26rpx;
		padding: 10rpx 20rpx;
	}

	/* 移动端编辑模式优化 */
	.detail-item.editing {
		margin-bottom: 35rpx;
		border-radius: 16rpx;
	}

	.detail-item.editing:not(:last-child)::after {
		bottom: -22rpx;
		width: 90%;
	}

	.edit-input,
	.edit-picker {
		font-size: 30rpx;
		padding: 14rpx 18rpx;
	}

	.add-detail-btn {
		font-size: 22rpx;
		padding: 6rpx 14rpx;
	}

	.delete-detail-btn {
		font-size: 20rpx;
		padding: 5rpx 10rpx;
	}
}

/* 月报模式青色主题 */
.monthly-theme .nav-bar {
	background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
	border-bottom: 2rpx solid rgba(0, 188, 212, 0.3);
	box-shadow: 0 2rpx 8rpx rgba(0, 188, 212, 0.2);
}

.monthly-theme .nav-icon {
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.monthly-theme .nav-text {
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.monthly-theme .nav-title {
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.monthly-theme .edit-btn {
	background-color: rgba(255, 255, 255, 0.2);
	color: white;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.monthly-theme .edit-btn:active {
	background-color: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

.monthly-theme .save-btn {
	background-color: #f24251;
}

.monthly-theme .save-btn:not(.disabled):active {
	background-color: #e11919;
}

.monthly-theme .back-btn {
	background-color: #00BCD4;
}

.monthly-theme .card-header {
	background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
}

.monthly-theme .info-value.earnings,
.monthly-theme .field-value.earnings,
.monthly-theme .summary-value.earnings {
	color: #00BCD4;
}

.monthly-theme .detail-title {
	color: #00BCD4;
}

.monthly-theme .edit-input:focus {
	border-color: #00BCD4;
}

/* 扣水量编辑容器 */
.moisture-edit-container {
	display: flex;
	align-items: center;
	gap: 5rpx;
}

.moisture-rate-input {
	width: 80rpx;
	text-align: center;
	padding: 12rpx 8rpx;
}

.moisture-unit {
	font-size: 32rpx;
	color: #333;
	font-weight: 500;
	white-space: nowrap;
}

/* 合并条目样式 */
.detail-header-view {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.merged-info {
	font-size: 22rpx;
	color: #666;
	background-color: #f0f0f0;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.edit-controls {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.readonly {
	color: #999 !important;
	font-style: italic;
}

.field-value.readonly {
	background-color: #f8f8f8;
	padding: 8rpx 12rpx;
	border-radius: 6rpx;
	border: 1rpx solid #e0e0e0;
}

.detail-time.readonly {
	font-size: 24rpx;
	color: #999;
	background-color: #f8f8f8;
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
}
</style>

