/**
 * 销售记录数据库操作类
 * 处理销售记录的CRUD操作和业务规则验证
 */

import LocalDatabase from './database.js'

class SalesRecordDB {
	constructor() {
		this.db = new LocalDatabase()
		this.tableName = 'sales_records'
	}

	/**
	 * 创建销售记录
	 * @param {Object} salesData 销售记录数据
	 * @returns {Object} 创建的销售记录
	 */
	async createSalesRecord(salesData) {
		try {
			// 数据验证
			const validationResult = this.validateSalesRecord(salesData)
			if (!validationResult.isValid) {
				throw new Error(validationResult.message)
			}

			// 验证日期有效性
			if (!salesData.date) {
				throw new Error('销售记录必须指定日期')
			}

			// 根据销售模式处理数据
			const salesMode = salesData.sales_mode || 'single_customer'
			let salesRecord

			if (salesMode === 'single_customer') {
				salesRecord = await this.createSingleCustomerRecord(salesData)
			} else if (salesMode === 'multiple_customers') {
				salesRecord = await this.createMultipleCustomersRecord(salesData)
			} else {
				throw new Error('无效的销售模式')
			}

			// 插入记录
			const newRecord = this.db.insert(this.tableName, salesRecord)
			console.log('销售记录创建成功:', newRecord)
			return newRecord

		} catch (error) {
			console.error('创建销售记录失败:', error)
			throw error
		}
	}

	/**
	 * 创建单客户销售记录
	 * @param {Object} salesData 销售记录数据
	 * @returns {Object} 销售记录
	 */
	async createSingleCustomerRecord(salesData) {
		// 计算总收入
		const totalIncome = this.calculateTotalIncome(salesData)

		// 获取该日期的工作记录信息（用于计算成本）
		const dailyCost = this.calculateDailyCost(salesData.date, salesData)

		// 计算毛利润
		const grossProfit = totalIncome - dailyCost

		return {
			date: salesData.date,
			sales_mode: 'single_customer',
			selling_price: parseFloat(salesData.selling_price) || 0,
			production: parseFloat(salesData.production) || 0,
			total_income: totalIncome,
			other_income: parseFloat(salesData.other_income) || 0,
			total_cost: dailyCost,
			other_cost: parseFloat(salesData.other_cost) || 0,
			gross_profit: grossProfit,
			customer_name: salesData.customer_name || salesData.customer || '',
			notes: salesData.notes || '',
			is_master_record: true,
			allocation_strategy: salesData.allocation_strategy || 'auto',
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
			sync_status: 'pending'
		}
	}

	/**
	 * 创建多客户销售记录
	 * @param {Object} salesData 销售记录数据
	 * @returns {Object} 销售记录
	 */
	async createMultipleCustomersRecord(salesData) {
		// 验证客户详情
		if (!salesData.customer_details || !Array.isArray(salesData.customer_details) || salesData.customer_details.length === 0) {
			throw new Error('多客户模式必须提供客户详情列表')
		}

		// 验证产量分配
		const totalAllocatedProduction = salesData.customer_details.reduce((sum, customer) => {
			return sum + (parseFloat(customer.production) || 0)
		}, 0)

		const dailyProduction = this.getDailyProduction(salesData.date)
		if (totalAllocatedProduction > dailyProduction) {
			throw new Error(`产量分配超出限制：分配${totalAllocatedProduction}斤，实际${dailyProduction}斤`)
		}

		// 计算汇总数据
		const totalIncome = salesData.customer_details.reduce((sum, customer) => {
			const customerIncome = (parseFloat(customer.selling_price) || 0) * (parseFloat(customer.production) || 0)
			return sum + customerIncome + (parseFloat(customer.other_income) || 0)
		}, 0)

		const totalOtherCost = salesData.customer_details.reduce((sum, customer) => {
			return sum + (parseFloat(customer.other_cost) || 0)
		}, 0)

		const dailyCost = this.calculateDailyCost(salesData.date, salesData)
		const grossProfit = totalIncome - dailyCost - totalOtherCost

		return {
			date: salesData.date,
			sales_mode: 'multiple_customers',
			selling_price: 0, // 多客户模式下单价无意义
			production: totalAllocatedProduction,
			total_income: totalIncome,
			other_income: 0, // 其他收入包含在客户详情中
			total_cost: dailyCost + totalOtherCost,
			other_cost: totalOtherCost,
			gross_profit: grossProfit,
			customer_name: `${salesData.customer_details.length}个客户`, // 汇总显示
			notes: salesData.notes || '',
			is_master_record: true,
			customer_details: salesData.customer_details.map((customer, index) => ({
				customer_id: customer.customer_id || `cust_${Date.now()}_${index}`,
				customer_name: customer.customer_name || '',
				selling_price: parseFloat(customer.selling_price) || 0,
				production: parseFloat(customer.production) || 0,
				total_income: (parseFloat(customer.selling_price) || 0) * (parseFloat(customer.production) || 0),
				other_income: parseFloat(customer.other_income) || 0,
				other_cost: parseFloat(customer.other_cost) || 0,
				notes: customer.notes || '',
				time_period_allocation: customer.time_period_allocation || {},
				allocation_priority: customer.allocation_priority || (index + 1)
			})),
			allocation_strategy: salesData.allocation_strategy || 'auto',
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
			sync_status: 'pending'
		}
	}

	/**
	 * 更新销售记录
	 * @param {String} id 销售记录ID
	 * @param {Object} updateData 更新数据
	 * @returns {Object} 更新后的销售记录
	 */
	async updateSalesRecord(id, updateData) {
		try {
			// 获取现有记录
			const existingRecord = this.getSalesRecordById(id)
			if (!existingRecord) {
				throw new Error('销售记录不存在')
			}

			// 合并更新数据
			const mergedData = { ...existingRecord, ...updateData }

			// 数据验证
			const validationResult = this.validateSalesRecord(mergedData)
			if (!validationResult.isValid) {
				throw new Error(validationResult.message)
			}

			// 重新计算相关字段
			const totalIncome = this.calculateTotalIncome(mergedData)
			const dailyCost = this.calculateDailyCost(mergedData.date, mergedData)
			const grossProfit = totalIncome - dailyCost

			// 构建更新数据
			const updateFields = {
				...updateData,
				total_income: totalIncome,
				total_cost: dailyCost,
				gross_profit: grossProfit,
				updated_at: new Date().toISOString(),
				sync_status: 'pending'
			}

			// 更新记录
			const updatedRecord = this.db.update(this.tableName, id, updateFields)
			console.log('销售记录更新成功:', updatedRecord)
			return updatedRecord

		} catch (error) {
			console.error('更新销售记录失败:', error)
			throw error
		}
	}

	/**
	 * 删除销售记录
	 * @param {String} id 销售记录ID
	 * @returns {Object} 删除的销售记录
	 */
	deleteSalesRecord(id) {
		try {
			const deletedRecord = this.db.delete(this.tableName, id)
			console.log('销售记录删除成功:', deletedRecord)
			return deletedRecord
		} catch (error) {
			console.error('删除销售记录失败:', error)
			throw error
		}
	}

	/**
	 * 根据ID获取销售记录
	 * @param {String} id 销售记录ID
	 * @returns {Object|null} 销售记录
	 */
	getSalesRecordById(id) {
		try {
			const records = this.db.select(this.tableName, {
				where: { id: id }
			})
			return records.length > 0 ? records[0] : null
		} catch (error) {
			console.error('获取销售记录失败:', error)
			return null
		}
	}

	/**
	 * 根据日期获取销售记录
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Array} 销售记录列表
	 */
	getSalesRecordsByDate(date) {
		try {
			// 首先尝试直接通过date字段查询（新版本）
			const directRecords = this.db.select(this.tableName, {
				where: { date: date },
				orderBy: { field: 'created_at', direction: 'desc' }
			})

			// 如果找到记录，直接返回
			if (directRecords.length > 0) {
				console.log(`📋 [SalesRecordDB] 通过date字段找到${directRecords.length}条销售记录`)
				return directRecords
			}

			// 如果没有找到，尝试通过work_record_id关联查询（向后兼容）
			console.log(`📋 [SalesRecordDB] 未通过date字段找到记录，尝试work_record_id关联查询`)
			const allSalesRecords = this.db.select(this.tableName)
			const workRecords = uni.getStorageSync('workRecords') || []

			const compatibleRecords = allSalesRecords.filter(salesRecord => {
				const workRecord = workRecords.find(wr => wr.id === salesRecord.work_record_id)
				return workRecord && workRecord.date === date
			})

			console.log(`📋 [SalesRecordDB] 通过work_record_id关联找到${compatibleRecords.length}条销售记录`)
			return compatibleRecords

		} catch (error) {
			console.error('获取日期关联的销售记录失败:', error)
			return []
		}
	}

	/**
	 * 根据工作记录ID获取销售记录 (向后兼容方法)
	 * @param {String} workRecordId 工作记录ID
	 * @returns {Array} 销售记录列表
	 */
	getSalesRecordsByWorkRecordId(workRecordId) {
		try {
			// 先获取工作记录的日期
			const workRecord = this.getWorkRecordById(workRecordId)
			if (!workRecord) {
				return []
			}

			// 根据日期查询销售记录
			return this.getSalesRecordsByDate(workRecord.date)
		} catch (error) {
			console.error('获取工作记录关联的销售记录失败:', error)
			return []
		}
	}

	/**
	 * 根据日期范围获取销售记录
	 * @param {String} startDate 开始日期
	 * @param {String} endDate 结束日期
	 * @returns {Array} 销售记录列表
	 */
	getSalesRecordsByDateRange(startDate, endDate) {
		try {
			const allRecords = this.db.select(this.tableName, {
				orderBy: { field: 'created_at', direction: 'desc' }
			})

			// 直接通过销售记录的日期进行筛选
			return allRecords.filter(record => {
				if (!record.date) return false

				const recordDate = new Date(record.date)
				const start = new Date(startDate)
				const end = new Date(endDate)

				return recordDate >= start && recordDate <= end
			})
		} catch (error) {
			console.error('根据日期范围获取销售记录失败:', error)
			return []
		}
	}

	/**
	 * 获取所有销售记录
	 * @param {Object} options 查询选项
	 * @returns {Array} 销售记录列表
	 */
	getAllSalesRecords(options = {}) {
		try {
			return this.db.select(this.tableName, {
				orderBy: { field: 'created_at', direction: 'desc' },
				...options
			})
		} catch (error) {
			console.error('获取所有销售记录失败:', error)
			return []
		}
	}

	/**
	 * 验证销售记录数据
	 * @param {Object} salesData 销售记录数据
	 * @returns {Object} 验证结果
	 */
	validateSalesRecord(salesData) {
		// 必填字段验证
		if (!salesData.date && !salesData.work_record_id) {
			return { isValid: false, message: '日期或工作记录ID不能同时为空' }
		}

		// 验证日期格式
		if (salesData.date) {
			const dateRegex = /^\d{4}-\d{2}-\d{2}$/
			if (!dateRegex.test(salesData.date)) {
				return { isValid: false, message: '日期格式必须为 YYYY-MM-DD' }
			}
		}

		// 验证销售模式
		const salesMode = salesData.sales_mode || 'single_customer'
		if (!['single_customer', 'multiple_customers'].includes(salesMode)) {
			return { isValid: false, message: '销售模式必须为 single_customer 或 multiple_customers' }
		}

		// 根据销售模式进行不同的验证
		if (salesMode === 'single_customer') {
			return this.validateSingleCustomerData(salesData)
		} else {
			return this.validateMultipleCustomersData(salesData)
		}
	}

	/**
	 * 验证单客户模式数据
	 * @param {Object} salesData 销售记录数据
	 * @returns {Object} 验证结果
	 */
	validateSingleCustomerData(salesData) {
		// 数值字段验证
		const sellingPrice = parseFloat(salesData.selling_price)
		if (isNaN(sellingPrice) || sellingPrice < 0) {
			return { isValid: false, message: '销售单价必须是非负数' }
		}

		const production = parseFloat(salesData.production)
		if (isNaN(production) || production < 0) {
			return { isValid: false, message: '产量必须是非负数' }
		}

		const otherIncome = parseFloat(salesData.other_income) || 0
		if (otherIncome < 0) {
			return { isValid: false, message: '其他收入不能为负数' }
		}

		const otherCost = parseFloat(salesData.other_cost) || 0
		if (otherCost < 0) {
			return { isValid: false, message: '其他支出不能为负数' }
		}

		return { isValid: true, message: '验证通过' }
	}

	/**
	 * 验证多客户模式数据
	 * @param {Object} salesData 销售记录数据
	 * @returns {Object} 验证结果
	 */
	validateMultipleCustomersData(salesData) {
		// 验证客户详情列表
		if (!salesData.customer_details || !Array.isArray(salesData.customer_details)) {
			return { isValid: false, message: '多客户模式必须提供客户详情列表' }
		}

		if (salesData.customer_details.length === 0) {
			return { isValid: false, message: '多客户模式至少需要一个客户' }
		}

		// 验证每个客户的数据
		for (let i = 0; i < salesData.customer_details.length; i++) {
			const customer = salesData.customer_details[i]

			if (!customer.customer_name || customer.customer_name.trim() === '') {
				return { isValid: false, message: `第${i + 1}个客户的姓名不能为空` }
			}

			const sellingPrice = parseFloat(customer.selling_price)
			if (isNaN(sellingPrice) || sellingPrice < 0) {
				return { isValid: false, message: `第${i + 1}个客户的销售单价必须是非负数` }
			}

			const production = parseFloat(customer.production)
			if (isNaN(production) || production < 0) {
				return { isValid: false, message: `第${i + 1}个客户的产量必须是非负数` }
			}

			const otherIncome = parseFloat(customer.other_income) || 0
			if (otherIncome < 0) {
				return { isValid: false, message: `第${i + 1}个客户的其他收入不能为负数` }
			}

			const otherCost = parseFloat(customer.other_cost) || 0
			if (otherCost < 0) {
				return { isValid: false, message: `第${i + 1}个客户的其他支出不能为负数` }
			}
		}

		// 验证产量分配总和
		const totalAllocatedProduction = salesData.customer_details.reduce((sum, customer) => {
			return sum + (parseFloat(customer.production) || 0)
		}, 0)

		if (salesData.date) {
			const dailyProduction = this.getDailyProduction(salesData.date)
			if (totalAllocatedProduction > dailyProduction) {
				return {
					isValid: false,
					message: `产量分配超出限制：分配${totalAllocatedProduction.toFixed(2)}斤，实际${dailyProduction.toFixed(2)}斤`
				}
			}
		}

		return { isValid: true, message: '验证通过' }
	}

	/**
	 * 计算总收入
	 * @param {Object} salesData 销售记录数据
	 * @returns {Number} 总收入
	 */
	calculateTotalIncome(salesData) {
		const salesIncome = (parseFloat(salesData.selling_price) || 0) * (parseFloat(salesData.production) || 0)
		const otherIncome = parseFloat(salesData.other_income) || 0
		return salesIncome + otherIncome
	}

	/**
	 * 计算某日期的总成本
	 * @param {String} date 日期
	 * @param {Object} salesData 销售数据
	 * @returns {Number} 总成本
	 */
	calculateDailyCost(date, salesData) {
		// 获取该日期所有工人的工作记录
		const workRecords = this.getWorkRecordsByDate(date)

		// 计算该日所有工人的工钱总和
		const dailyLaborCost = workRecords.reduce((total, record) => {
			return total + (record.total_earnings || 0)
		}, 0)

		const otherCost = parseFloat(salesData.other_cost) || 0
		return dailyLaborCost + otherCost
	}

	/**
	 * 计算总成本 (向后兼容方法)
	 * @param {Object} workRecord 工作记录
	 * @param {Object} salesData 销售记录数据
	 * @returns {Number} 总成本
	 */
	calculateTotalCost(workRecord, salesData) {
		const workRecordCost = parseFloat(workRecord?.total_earnings) || 0
		const otherCost = parseFloat(salesData.other_cost) || 0
		return workRecordCost + otherCost
	}

	/**
	 * 获取工作记录
	 * @param {String} workRecordId 工作记录ID
	 * @returns {Object|null} 工作记录
	 */
	getWorkRecordById(workRecordId) {
		try {
			const workRecords = uni.getStorageSync('workRecords') || []

			// 尝试多种匹配方式以处理不同的ID格式
			return workRecords.find(record => {
				// 直接比较
				if (record.id === workRecordId) return true
				// 字符串比较
				if (record.id === workRecordId.toString()) return true
				// 数字比较
				if (record.id === Number(workRecordId)) return true
				// 都转为字符串比较
				if (record.id.toString() === workRecordId.toString()) return true
				return false
			}) || null
		} catch (error) {
			console.error('获取工作记录失败:', error)
			return null
		}
	}

	/**
	 * 从工作记录计算产量
	 * @param {Object} workRecord 工作记录
	 * @returns {Number} 产量
	 */
	calculateProductionFromWorkRecord(workRecord) {
		if (workRecord.work_mode === 'tea_picking' && workRecord.tea_picking_details) {
			return workRecord.tea_picking_details.reduce((total, detail) => {
				return total + (parseFloat(detail.actual_weight) || 0)
			}, 0)
		}
		return 0
	}

	/**
	 * 根据日期获取工作记录
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Array} 工作记录列表
	 */
	getWorkRecordsByDate(date) {
		try {
			const workRecords = uni.getStorageSync('workRecords') || []
			return workRecords.filter(record => record.date === date)
		} catch (error) {
			console.error('获取日期工作记录失败:', error)
			return []
		}
	}

	/**
	 * 获取指定日期的总产量
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Number} 总产量
	 */
	getDailyProduction(date) {
		try {
			const workRecords = this.getWorkRecordsByDate(date)
			return workRecords.reduce((total, record) => {
				return total + this.calculateProductionFromWorkRecord(record)
			}, 0)
		} catch (error) {
			console.error('获取日期总产量失败:', error)
			return 0
		}
	}

	/**
	 * 获取指定日期的时间段产量分布
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Object} 时间段产量分布
	 */
	getDailyTimePeriodProduction(date) {
		try {
			const workRecords = this.getWorkRecordsByDate(date)
			const timePeriodProduction = {
				morning: 0,
				afternoon: 0
			}

			workRecords.forEach(record => {
				if (record.work_mode === 'tea_picking' && record.tea_picking_details) {
					record.tea_picking_details.forEach(detail => {
						const production = parseFloat(detail.actual_weight) || 0
						let timePeriod = detail.time_period || 'morning'

						// 向后兼容：将历史数据中的 evening 和 noon 映射到 afternoon
						if (timePeriod === 'evening' || timePeriod === 'noon') {
							timePeriod = 'afternoon'
							console.log('⚠️ 检测到历史数据中的', detail.time_period, '时间段，已映射到afternoon')
						}

						if (timePeriodProduction.hasOwnProperty(timePeriod)) {
							timePeriodProduction[timePeriod] += production
						} else {
							// 如果是未知时间段，默认归类到上午
							console.warn('⚠️ 未知时间段:', timePeriod, '已归类到morning')
							timePeriodProduction.morning += production
						}
					})
				}
			})

			return timePeriodProduction
		} catch (error) {
			console.error('获取时间段产量分布失败:', error)
			return { morning: 0, afternoon: 0 }
		}
	}

	/**
	 * 智能产量分配算法
	 * @param {String} date 日期
	 * @param {Number} customerCount 客户数量
	 * @returns {Array} 分配方案
	 */
	calculateSmartAllocation(date, customerCount) {
		try {
			const timePeriodProduction = this.getDailyTimePeriodProduction(date)
			const totalProduction = this.getDailyProduction(date)

			console.log('🧠 智能分配算法开始:', {
				date,
				customerCount,
				timePeriodProduction,
				totalProduction
			})

			if (customerCount <= 0 || totalProduction <= 0) {
				return []
			}

			const allocations = []

			if (customerCount === 1) {
				// 单客户：分配全部产量
				allocations.push({
					customerIndex: 0,
					production: totalProduction,
					timePeriodAllocation: timePeriodProduction,
					strategy: 'all_production'
				})
			} else if (customerCount === 2) {
				// 双客户：第一客户上午，第二客户下午
				const customer1Production = timePeriodProduction.morning
				const customer2Production = timePeriodProduction.afternoon

				allocations.push({
					customerIndex: 0,
					production: customer1Production,
					timePeriodAllocation: {
						morning: timePeriodProduction.morning,
						afternoon: 0
					},
					strategy: 'morning'
				})

				allocations.push({
					customerIndex: 1,
					production: customer2Production,
					timePeriodAllocation: {
						morning: 0,
						afternoon: timePeriodProduction.afternoon
					},
					strategy: 'afternoon'
				})
			} else {
				// 多客户：第一客户上午，第二客户下午，其余客户产量为0
				const customer1Production = timePeriodProduction.morning
				const customer2Production = timePeriodProduction.afternoon

				allocations.push({
					customerIndex: 0,
					production: customer1Production,
					timePeriodAllocation: {
						morning: timePeriodProduction.morning,
						afternoon: 0
					},
					strategy: 'morning'
				})

				allocations.push({
					customerIndex: 1,
					production: customer2Production,
					timePeriodAllocation: {
						morning: 0,
						afternoon: timePeriodProduction.afternoon
					},
					strategy: 'afternoon'
				})

				// 其余客户产量为0，需要手动分配
				for (let i = 2; i < customerCount; i++) {
					allocations.push({
						customerIndex: i,
						production: 0,
						timePeriodAllocation: {
							morning: 0,
							afternoon: 0
						},
						strategy: 'manual'
					})
				}
			}

			console.log('🎯 智能分配结果:', allocations)
			return allocations
		} catch (error) {
			console.error('智能产量分配失败:', error)
			return []
		}
	}

	/**
	 * 计算某日期的总产量
	 * @param {String} date 日期 (YYYY-MM-DD)
	 * @returns {Number} 总产量
	 */
	calculateDailyTotalProduction(date) {
		const workRecords = this.getWorkRecordsByDate(date)
		return workRecords.reduce((total, workRecord) => {
			if (workRecord.work_mode === 'tea_picking' && workRecord.tea_picking_details) {
				const recordProduction = this.calculateWorkRecordProduction(workRecord)
				return total + recordProduction
			}
			return total
		}, 0)
	}

	/**
	 * 计算单个工作记录的产量
	 * @param {Object} workRecord 工作记录
	 * @returns {Number} 产量
	 */
	calculateWorkRecordProduction(workRecord) {
		if (workRecord.work_mode === 'tea_picking' && workRecord.tea_picking_details) {
			if (Array.isArray(workRecord.tea_picking_details)) {
				return workRecord.tea_picking_details.reduce((total, detail) => {
					return total + (parseFloat(detail.actual_weight) || 0)
				}, 0)
			} else if (typeof workRecord.tea_picking_details === 'object') {
				return parseFloat(workRecord.tea_picking_details.actual_weight) || 0
			}
		}
		return 0
	}
}

export default SalesRecordDB
