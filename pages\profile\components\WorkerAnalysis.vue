<template>
	<view class="worker-analysis">
		<!-- 统一选择器面板 -->
		<view class="unified-selector">
			<!-- 标题区域 -->
			<view class="selector-title-section">
				<text class="main-title">工人分析</text>
			</view>

			<!-- 选择器内容区域 -->
			<view class="selector-content">
				<!-- 工人选择区域 -->
				<view class="worker-selection-area">

					<view class="name-input-wrapper"
						:class="{ 'suggestions-active': showNameSuggestions && filteredNames.length > 0 }">
						<input class="form-input" type="text" placeholder="请输入或选择工人姓名" v-model="workerNameInput"
							@input="onNameInput" @focus="onNameFocus" />
						<text class="input-icon" @click="showNameLibrary">📝</text>

						<!-- 姓名建议列表 -->
						<view class="name-suggestions" v-if="showNameSuggestions && filteredNames.length > 0">
							<view class="suggestion-item" v-for="name in filteredNames" :key="name"
								@click="selectName(name)">
								<text>{{ name }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 日期范围选择区域 -->
				<view class="date-selection-area">

					<view class="date-range-inputs">
						<picker mode="date" :value="dateRange[0]" @change="onStartDateChange">
							<view class="date-picker">
								<text class="date-text">{{ dateRange[0] || getCurrentMonthStart() }}</text>
							</view>
						</picker>
						<text class="date-separator">~</text>
						<picker mode="date" :value="dateRange[1]" @change="onEndDateChange">
							<view class="date-picker">
								<text class="date-text">{{ dateRange[1] || getCurrentMonthEnd() }}</text>
							</view>
						</picker>
						<!-- 回到本月按钮 -->
						<view class="reset-button" v-if="!isCurrentMonth" @click="resetToCurrentMonth">
							<text class="reset-text">本月</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 姓名建议遮罩层 -->
			<view class="suggestions-overlay" v-if="showNameSuggestions && filteredNames.length > 0"
				@click="hideNameSuggestions"></view>
		</view>

		<!-- 工人概览 -->
		<view class="worker-overview" v-if="selectedWorker && workerRecords.length > 0">
			<view class="selector-title-section">
				<text class="main-title">{{ selectedWorker }} - 工作概览</text>
			</view>

			<view class="overview-cards">
				<view class="overview-card">
					<text class="card-value">¥{{ $formatCurrency(workerStats.totalEarnings) }}</text>
					<text class="card-label">总收入</text>
				</view>
				<view class="overview-card">
					<text class="card-value">{{ workerStats.totalQuantity }}kg</text>
					<text class="card-label">总产量</text>
				</view>
				<view class="overview-card">
					<text class="card-value">{{ workerStats.totalDays }}</text>
					<text class="card-label">出勤天数</text>
				</view>

				<view class="overview-card">
					<text class="card-value">¥{{ $formatCurrency(workerStats.avgDailyEarnings) }}</text>
					<text class="card-label">日均收入</text>
				</view>

			</view>
		</view>

		<!-- 出勤情况分析 -->
		<view class="attendance-section" v-if="selectedWorker && workerRecords.length > 0">

			<view class="selector-title-section">
				<text class="main-title">出勤情况分析</text>
			</view>
			<!-- 工作模式分布 -->
			<view class="work-mode-distribution">
				<view class="mode-stats">
					<view class="mode-item">
						<text class="mode-name">采茶模式</text>
						<text class="mode-days">{{ workModeStats.teaPicking.days }}天</text>
						<text class="mode-percentage">{{ workModeStats.teaPicking.percentage }}%</text>
					</view>
					<view class="mode-item">
						<text class="mode-name">时工模式</text>
						<text class="mode-days">{{ workModeStats.hourly.days }}天</text>
						<text class="mode-percentage">{{ workModeStats.hourly.percentage }}%</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 收入情况分析 -->
		<view class="earnings-section" v-if="selectedWorker && workerRecords.length > 0">

			<view class="selector-title-section">
				<text class="main-title">收入情况分析</text>
			</view>

			<!-- 收入统计 -->
			<view class="earnings-stats">
				<view class="stats-grid">
					<view class="stat-item">
						<text class="stat-label">最高日收入</text>
						<text class="stat-value">¥{{ $formatCurrency(earningsStats.maxDaily) }}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">最低日收入</text>
						<text class="stat-value">¥{{ $formatCurrency(earningsStats.minDaily) }}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">最高日产量</text>
						<text class="stat-value">{{ efficiencyStats.teaPicking.maxQuantity }}kg</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">最低日产量</text>
						<text class="stat-value">{{ efficiencyStats.teaPicking.minQuantity }}kg</text>
					</view>

				</view>
			</view>
		</view>

		<!-- 工人对比排名 -->
		<view class="ranking-section" v-if="selectedWorker && workerRecords.length > 0">
			<view class="selector-title-section">
				<text class="main-title">工人对比排名</text>
			</view>

			<!-- 符合排名条件时显示排名信息 -->
			<view class="ranking-cards" v-if="workerRanking.isEligible">
				<view class="ranking-card">
					<text class="ranking-title">收入排名</text>
					<text class="ranking-position">第 {{ workerRanking.earnings }} 名</text>
					<text class="ranking-desc">共 {{ workerRanking.totalWorkers }} 人参与排名</text>
					<text class="ranking-percent">超越了 {{ workerRanking.earningsPercent }}% 的工人</text>
				</view>
				<view class="ranking-card">
					<text class="ranking-title">出勤排名</text>
					<text class="ranking-position">第 {{ workerRanking.attendance }} 名</text>
					<text class="ranking-desc">基于采茶出勤天数排名</text>
					<text class="ranking-percent">超越了 {{ workerRanking.attendancePercent }}% 的工人</text>
				</view>
				<view class="ranking-card">
					<text class="ranking-title">效率排名</text>
					<text class="ranking-position">第 {{ workerRanking.efficiency }} 名</text>
					<text class="ranking-desc">基于采茶效率排名</text>
					<text class="ranking-percent">超越了 {{ workerRanking.efficiencyPercent }}% 的工人</text>
				</view>
			</view>

			<!-- 不符合排名条件时显示提示信息 -->
			<view class="ranking-ineligible" v-else>
				<text class="ineligible-icon">📊</text>
				<text class="ineligible-title">未达到排名条件</text>
				<text class="ineligible-desc">需要采茶工作天数超过5天才能参与排名</text>
				<text class="ineligible-suggestion">当前采茶天数：{{ workModeStats.teaPicking.days }}天</text>
			</view>
		</view>

		<!-- 无数据提示 -->
		<view class="no-data" v-if="!selectedWorker">
			<text class="no-data-icon">👤</text>
			<text class="no-data-title">请选择工人</text>
			<text class="no-data-desc">选择一个工人查看详细的工作分析</text>
		</view>

		<!-- 选择了工人但无数据提示 -->
		<view class="no-data" v-if="selectedWorker && workerRecords.length === 0">
			<text class="no-data-icon">📊</text>
			<text class="no-data-title">暂无数据</text>
			<text class="no-data-desc">{{ selectedWorker }} 在所选日期范围内没有工作记录</text>
			<text class="no-data-suggestion">请尝试调整日期范围或选择其他工人</text>
		</view>

		<!-- 调试信息（开发时可启用） -->
		<!-- <view class="debug-info" v-if="debugInfo && false">
			<text class="debug-title">数据调试信息</text>
			<text class="debug-item">工人: {{ debugInfo.worker }}</text>
			<text class="debug-item">日期范围: {{ debugInfo.dateRange }}</text>
			<text class="debug-item">总记录数: {{ debugInfo.totalRecords }}</text>
			<text class="debug-item">过滤后记录数: {{ debugInfo.filteredRecords }}</text>
			<text class="debug-item">有数据: {{ debugInfo.hasData ? '是' : '否' }}</text>
		</view> -->

		<!-- 排名调试信息（开发时可启用） -->
		<!-- <view class="debug-info" v-if="rankingDebugInfo && false">
			<text class="debug-title">排名调试信息</text>
			<text class="debug-item">当前工人: {{ rankingDebugInfo.currentWorker.workerName }}</text>
			<text class="debug-item">总工人数: {{ rankingDebugInfo.totalWorkers }}</text>
			<text class="debug-subtitle">收入排名:</text>
			<text class="debug-item" v-for="rank in rankingDebugInfo.earningsRank" :key="rank">{{ rank }}</text>
			<text class="debug-subtitle">出勤排名:</text>
			<text class="debug-item" v-for="rank in rankingDebugInfo.attendanceRank" :key="rank">{{ rank }}</text>
			<text class="debug-subtitle">效率排名:</text>
			<text class="debug-item" v-for="rank in rankingDebugInfo.efficiencyRank" :key="rank">{{ rank }}</text>
		</view> -->

		<!-- 姓名库弹窗 -->
		<view class="modal-overlay" v-if="showNameLibraryModal" @click="showNameLibraryModal = false">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">选择工人姓名</text>
					<text class="modal-close" @click="showNameLibraryModal = false">✕</text>
				</view>
				<view class="name-list">
					<view class="name-item" v-for="name in nameLibrary" :key="name"
						@click="selectNameFromLibrary(name)">
						<view class="name-content">
							<text class="name-text">{{ name }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'WorkerAnalysis',
	props: {
		records: {
			type: Array,
			default: () => []
		},
		nameLibrary: {
			type: Array,
			default: () => []
		},
		selectedWorker: {
			type: String,
			default: ''
		},
		dateRange: {
			type: Array,
			default: () => ['', '']
		}
	},
	data() {
		return {
			workerNameInput: '', // 工人姓名输入框的值
			showNameSuggestions: false, // 是否显示姓名建议列表
			showNameLibraryModal: false, // 是否显示姓名库弹窗
			workerStats: {
				totalDays: 0,
				totalEarnings: 0,
				avgDailyEarnings: 0,
				totalQuantity: 0,
				attendanceRate: 0
			},
			workModeStats: {
				teaPicking: { days: 0, percentage: 0 },
				hourly: { days: 0, percentage: 0 }
			},
			earningsStats: {
				maxDaily: 0,
				minDaily: 0
			},
			efficiencyStats: {
				teaPicking: {
					totalDays: 0,
					avgQuantity: 0,
					maxQuantity: 0,
					minQuantity: 0,
					stability: '稳定'
				},
				hourly: {
					totalDays: 0,
					avgHours: 0,
					avgHourlyRate: 0,
					overtimeRate: 0
				}
			},
			workerRanking: {
				earnings: 1,
				attendance: 1,
				efficiency: 1,
				totalWorkers: 1,
				earningsPercent: 0,
				attendancePercent: 0,
				efficiencyPercent: 0,
				isEligible: false
			}
		}
	},
	computed: {
		// 过滤后的姓名列表（用于自动补全）
		filteredNames() {
			if (!this.nameLibrary || this.nameLibrary.length === 0) return []

			if (!this.workerNameInput.trim()) {
				return this.nameLibrary.slice(0, 10) // 显示前10个
			}

			return this.nameLibrary.filter(name =>
				name.toLowerCase().includes(this.workerNameInput.toLowerCase())
			).slice(0, 10)
		},

		workerRecords() {
			if (!this.selectedWorker || this.selectedWorker === '全部工人') {
				return []
			}

			// 确保records数组存在且有效
			if (!Array.isArray(this.records)) {
				console.warn('WorkerAnalysis: records is not an array:', this.records)
				return []
			}

			// 过滤工人姓名和日期范围
			return this.records.filter(record => {
				// 验证记录数据有效性
				if (!this.isValidRecord(record)) {
					console.warn('WorkerAnalysis: Invalid record:', record)
					return false
				}

				// 检查工人姓名
				if (record.worker_name !== this.selectedWorker) {
					return false
				}

				// 检查日期范围
				if (this.dateRange[0] && this.dateRange[1]) {
					const recordDate = record.date
					// 确保记录日期在选定范围内
					return recordDate >= this.dateRange[0] && recordDate <= this.dateRange[1]
				}

				// 如果没有设置日期范围，使用当前月份作为默认范围
				const currentMonthStart = this.getCurrentMonthStart()
				const currentMonthEnd = this.getCurrentMonthEnd()
				const recordDate = record.date
				return recordDate >= currentMonthStart && recordDate <= currentMonthEnd
			})
		},

		// 判断当前选择的日期范围是否为本月
		isCurrentMonth() {
			const currentMonthStart = this.getCurrentMonthStart()
			const currentMonthEnd = this.getCurrentMonthEnd()

			const selectedStart = this.dateRange[0] || currentMonthStart
			const selectedEnd = this.dateRange[1] || currentMonthEnd

			return selectedStart === currentMonthStart && selectedEnd === currentMonthEnd
		},

		// 调试信息：显示当前过滤条件和结果
		debugInfo() {
			if (!this.selectedWorker) return null

			const totalRecords = this.records.filter(r => r.worker_name === this.selectedWorker).length
			const dateFilteredRecords = this.workerRecords.length
			const dateRange = this.dateRange[0] && this.dateRange[1]
				? `${this.dateRange[0]} ~ ${this.dateRange[1]}`
				: `${this.getCurrentMonthStart()} ~ ${this.getCurrentMonthEnd()}`

			return {
				worker: this.selectedWorker,
				dateRange,
				totalRecords,
				filteredRecords: dateFilteredRecords,
				hasData: dateFilteredRecords > 0
			}
		},

		// 排名调试信息
		rankingDebugInfo() {
			if (!this.selectedWorker || this.workerRecords.length === 0) return null

			const allWorkersStats = this.getAllWorkersStats()
			const currentWorkerStats = allWorkersStats.find(stats => stats.workerName === this.selectedWorker)

			if (!currentWorkerStats) return null

			// 收入排序
			const earningsSorted = [...allWorkersStats].sort((a, b) => b.totalEarnings - a.totalEarnings)
			// 出勤排序
			const attendanceSorted = [...allWorkersStats].sort((a, b) => b.totalDays - a.totalDays)
			// 效率排序
			const workersWithTeaRecords = allWorkersStats.filter(stats => stats.avgDailyQuantity > 0)
			const efficiencySorted = [...workersWithTeaRecords].sort((a, b) => b.avgDailyQuantity - a.avgDailyQuantity)

			return {
				currentWorker: currentWorkerStats,
				totalWorkers: allWorkersStats.length,
				earningsRank: earningsSorted.map((w, i) => `${i+1}. ${w.workerName}(¥${w.totalEarnings})`),
				attendanceRank: attendanceSorted.map((w, i) => `${i+1}. ${w.workerName}(${w.totalDays}天)`),
				efficiencyRank: efficiencySorted.map((w, i) => `${i+1}. ${w.workerName}(${w.avgDailyQuantity}kg)`)
			}
		}
	},
	watch: {
		workerRecords: {
			handler() {
				this.calculateWorkerStats()
			},
			immediate: true
		},

		// 监听selectedWorker变化，同步到输入框
		selectedWorker: {
			handler(newVal) {
				this.workerNameInput = newVal || ''
			},
			immediate: true
		}
	},
	methods: {
		// 姓名输入框获得焦点
		onNameFocus() {
			if (this.nameLibrary && this.nameLibrary.length > 0) {
				this.showNameSuggestions = true
			}
		},

		// 姓名输入框输入事件
		onNameInput() {
			if (this.nameLibrary && this.nameLibrary.length > 0) {
				this.showNameSuggestions = true
			}
			// 实时更新选中的工人
			this.$emit('worker-change', this.workerNameInput.trim())
		},

		// 选择姓名建议
		selectName(name) {
			this.workerNameInput = name
			this.showNameSuggestions = false
			this.$emit('worker-change', name)
		},

		// 隐藏姓名建议列表
		hideNameSuggestions() {
			this.showNameSuggestions = false
		},

		// 显示姓名库弹窗
		showNameLibrary() {
			this.showNameLibraryModal = true
		},

		// 从姓名库选择姓名
		selectNameFromLibrary(name) {
			this.workerNameInput = name
			this.showNameLibraryModal = false
			this.showNameSuggestions = false
			this.$emit('worker-change', name)
		},

		// 保留原有的方法以兼容
		onWorkerChange(e) {
			const worker = this.workerOptions[e.detail.value]
			this.$emit('worker-change', worker === '全部工人' ? '' : worker)
		},

		onStartDateChange(e) {
			const newRange = [e.detail.value, this.dateRange[1]]
			this.$emit('date-range-change', newRange)
		},

		onEndDateChange(e) {
			const newRange = [this.dateRange[0], e.detail.value]
			this.$emit('date-range-change', newRange)
		},

		// 获取当前月份的开始日期
		getCurrentMonthStart() {
			const now = new Date()
			const year = now.getFullYear()
			const month = now.getMonth()
			// 使用本地时间格式化，避免时区问题
			const startDate = new Date(year, month, 1)
			return this.formatDate(startDate)
		},

		// 获取当前月份的结束日期
		getCurrentMonthEnd() {
			const now = new Date()
			const year = now.getFullYear()
			const month = now.getMonth()
			// 获取下个月的第0天，即当前月的最后一天
			const endDate = new Date(year, month + 1, 0)
			return this.formatDate(endDate)
		},

		// 格式化日期为 YYYY-MM-DD 格式，使用本地时间
		formatDate(date) {
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},

		// 验证记录数据的有效性
		isValidRecord(record) {
			// 检查必要字段
			if (!record || typeof record !== 'object') {
				return false
			}

			// 检查工人姓名
			if (!record.worker_name || typeof record.worker_name !== 'string') {
				return false
			}

			// 检查日期格式 (YYYY-MM-DD)
			if (!record.date || !/^\d{4}-\d{2}-\d{2}$/.test(record.date)) {
				return false
			}

			// 检查日期是否有效
			const date = new Date(record.date)
			if (isNaN(date.getTime())) {
				return false
			}

			return true
		},

		// 重置到当前月份
		resetToCurrentMonth() {
			const currentMonthStart = this.getCurrentMonthStart()
			const currentMonthEnd = this.getCurrentMonthEnd()
			const newRange = [currentMonthStart, currentMonthEnd]
			this.$emit('date-range-change', newRange)
		},

		calculateWorkerStats() {
			if (!this.workerRecords.length) {
				this.resetStats()
				return
			}

			// 基本统计
			const totalDays = this.workerRecords.length
			const totalEarnings = this.workerRecords.reduce((sum, record) =>
				sum + parseFloat(record.total_earnings || 0), 0)
			const avgDailyEarnings = totalEarnings / totalDays

			// 计算总产量（只统计采茶模式的产量）
			const totalQuantity = this.workerRecords
				.filter(record => record.work_mode === 'tea_picking' && record.tea_picking_details)
				.reduce((sum, record) => {
					// 处理数组格式的tea_picking_details
					if (Array.isArray(record.tea_picking_details)) {
						return sum + record.tea_picking_details.reduce((detailSum, detail) =>
							detailSum + parseFloat(detail.actual_weight || detail.weight || 0), 0)
					}
					// 处理对象格式的tea_picking_details
					return sum + parseFloat(record.tea_picking_details.actual_weight || record.tea_picking_details.weight || 0)
				}, 0)

			// 计算出勤率（基于日期范围）
			const dateRange = this.getDateRangeLength()
			const attendanceRate = dateRange > 0 ? (totalDays / dateRange * 100) : 100

			this.workerStats = {
				totalDays,
				totalEarnings,
				avgDailyEarnings,
				totalQuantity: totalQuantity.toFixed(1),
				attendanceRate: Math.min(100, attendanceRate).toFixed(1)
			}

			// 工作模式统计
			this.calculateWorkModeStats()

			// 收入统计
			this.calculateEarningsStats()

			// 效率统计
			this.calculateEfficiencyStats()

			// 排名统计（简化处理）
			this.calculateRanking()
		},

		calculateWorkModeStats() {
			const teaPickingDays = this.workerRecords.filter(r => r.work_mode === 'tea_picking').length
			const hourlyDays = this.workerRecords.filter(r => r.work_mode === 'hourly').length
			const totalDays = this.workerRecords.length

			this.workModeStats = {
				teaPicking: {
					days: teaPickingDays,
					percentage: totalDays > 0 ? (teaPickingDays / totalDays * 100).toFixed(1) : 0
				},
				hourly: {
					days: hourlyDays,
					percentage: totalDays > 0 ? (hourlyDays / totalDays * 100).toFixed(1) : 0
				}
			}
		},

		calculateEarningsStats() {
			// 过滤出有效的收入数据（排除0值和无效数据）
			const validEarnings = this.workerRecords
				.map(r => parseFloat(r.total_earnings || 0))
				.filter(earning => earning > 0)

			if (validEarnings.length === 0) {
				this.earningsStats = {
					maxDaily: 0,
					minDaily: 0
				}
				return
			}

			const maxDaily = Math.max(...validEarnings)
			const minDaily = Math.min(...validEarnings)

			this.earningsStats = {
				maxDaily,
				minDaily
			}
		},

		calculateEfficiencyStats() {
			// 采茶效率
			const teaRecords = this.workerRecords.filter(r => r.work_mode === 'tea_picking')
			const teaQuantities = teaRecords
				.filter(r => r.tea_picking_details)
				.map(r => {
					// 处理数组格式的tea_picking_details
					if (Array.isArray(r.tea_picking_details)) {
						return r.tea_picking_details.reduce((sum, detail) =>
							sum + parseFloat(detail.actual_weight || detail.weight || 0), 0)
					}
					// 处理对象格式的tea_picking_details
					return parseFloat(r.tea_picking_details.actual_weight || r.tea_picking_details.weight || 0)
				})
				.filter(quantity => quantity > 0) // 过滤掉0值和无效数据

			const teaPickingStats = {
				totalDays: teaRecords.length,
				avgQuantity: teaQuantities.length > 0 ?
					(teaQuantities.reduce((sum, q) => sum + q, 0) / teaQuantities.length).toFixed(1) : 0,
				maxQuantity: teaQuantities.length > 0 ? Math.max(...teaQuantities).toFixed(1) : 0,
				minQuantity: teaQuantities.length > 0 ? Math.min(...teaQuantities).toFixed(1) : 0,
				stability: '稳定' // 简化处理
			}

			// 时工效率
			const hourlyRecords = this.workerRecords.filter(r => r.work_mode === 'hourly')
			const hourlyDetails = hourlyRecords
				.filter(r => r.hourly_work_details)
				.map(r => r.hourly_work_details)

			const totalHours = hourlyDetails.reduce((sum, d) => sum + parseFloat(d.total_hours || 0), 0)
			const totalRate = hourlyDetails.reduce((sum, d) => sum + parseFloat(d.hourly_rate || 0), 0)
			const overtimeCount = hourlyDetails.filter(d => parseFloat(d.overtime_hours || 0) > 0).length

			const hourlyStats = {
				totalDays: hourlyRecords.length,
				avgHours: hourlyRecords.length > 0 ? (totalHours / hourlyRecords.length).toFixed(1) : 0,
				avgHourlyRate: hourlyDetails.length > 0 ? (totalRate / hourlyDetails.length).toFixed(2) : 0,
				overtimeRate: hourlyRecords.length > 0 ? (overtimeCount / hourlyRecords.length * 100).toFixed(1) : 0
			}

			this.efficiencyStats = {
				teaPicking: teaPickingStats,
				hourly: hourlyStats
			}
		},

		calculateRanking() {
			if (!this.selectedWorker || this.selectedWorker === '全部工人' || !this.records.length) {
				this.workerRanking = {
					earnings: 1,
					attendance: 1,
					efficiency: 1,
					totalWorkers: 1,
					earningsPercent: 0,
					attendancePercent: 0,
					efficiencyPercent: 0,
					isEligible: false
				}
				return
			}

			// 获取当前工人的统计数据
			const allWorkersStats = this.getAllWorkersStats()
			const currentWorkerStats = allWorkersStats.find(stats => stats.workerName === this.selectedWorker)

			if (!currentWorkerStats) {
				this.workerRanking = {
					earnings: 1,
					attendance: 1,
					efficiency: 1,
					totalWorkers: 1,
					earningsPercent: 0,
					attendancePercent: 0,
					efficiencyPercent: 0,
					isEligible: false
				}
				return
			}

			// 检查当前工人是否符合排名条件（采茶工作天数 > 5天）
			const isEligible = currentWorkerStats.teaPickingDays > 5

			if (!isEligible) {
				// 当前工人不符合排名条件
				this.workerRanking = {
					earnings: 1,
					attendance: 1,
					efficiency: 1,
					totalWorkers: 1,
					earningsPercent: 0,
					attendancePercent: 0,
					efficiencyPercent: 0,
					isEligible: false
				}
				return
			}

			// 获取符合排名条件的工人统计数据（采茶工作天数 > 5天）
			const eligibleWorkersStats = this.getEligibleWorkersStats()

			if (eligibleWorkersStats.length === 0) {
				this.workerRanking = {
					earnings: 1,
					attendance: 1,
					efficiency: 1,
					totalWorkers: 1,
					earningsPercent: 0,
					attendancePercent: 0,
					efficiencyPercent: 0,
					isEligible: false
				}
				return
			}

			const totalEligibleWorkers = eligibleWorkersStats.length

			// 计算收入排名（降序：收入高的排名靠前）
			const earningsSorted = [...eligibleWorkersStats].sort((a, b) => b.totalEarnings - a.totalEarnings)
			const earningsRanking = earningsSorted.findIndex(stats => stats.workerName === this.selectedWorker) + 1

			// 计算出勤排名（降序：采茶天数多的排名靠前）
			const attendanceSorted = [...eligibleWorkersStats].sort((a, b) => b.teaPickingDays - a.teaPickingDays)
			const attendanceRanking = attendanceSorted.findIndex(stats => stats.workerName === this.selectedWorker) + 1

			// 计算效率排名（降序：平均日产量高的排名靠前）
			// 所有符合条件的工人都有采茶记录，因为筛选条件就是采茶天数 > 5
			const efficiencySorted = [...eligibleWorkersStats].sort((a, b) => b.avgDailyQuantity - a.avgDailyQuantity)
			const efficiencyRanking = efficiencySorted.findIndex(stats => stats.workerName === this.selectedWorker) + 1

			// 计算百分位（超越了多少百分比的符合条件工人）
			const earningsPercent = totalEligibleWorkers > 1 ?
				Math.round((totalEligibleWorkers - earningsRanking) / (totalEligibleWorkers - 1) * 100) : 100
			const attendancePercent = totalEligibleWorkers > 1 ?
				Math.round((totalEligibleWorkers - attendanceRanking) / (totalEligibleWorkers - 1) * 100) : 100
			const efficiencyPercent = totalEligibleWorkers > 1 ?
				Math.round((totalEligibleWorkers - efficiencyRanking) / (totalEligibleWorkers - 1) * 100) : 100

			this.workerRanking = {
				earnings: earningsRanking,
				attendance: attendanceRanking,
				efficiency: efficiencyRanking,
				totalWorkers: totalEligibleWorkers,
				earningsPercent,
				attendancePercent,
				efficiencyPercent,
				isEligible: true
			}
		},

		getAllWorkersStats() {
			// 确保records数组存在且有效
			if (!Array.isArray(this.records)) {
				return []
			}

			// 获取所有工人的姓名列表（只包含有效记录）
			const allWorkerNames = [...new Set(
				this.records
					.filter(record => this.isValidRecord(record))
					.map(record => record.worker_name)
			)]

			// 为每个工人计算统计数据
			const allWorkersData = allWorkerNames.map(workerName => {
				// 获取该工人在当前日期范围内的记录（使用与workerRecords相同的过滤逻辑）
				const workerRecords = this.records.filter(record => {
					// 验证记录数据有效性
					if (!this.isValidRecord(record)) {
						return false
					}

					// 检查工人姓名
					if (record.worker_name !== workerName) {
						return false
					}

					// 检查日期范围（与workerRecords计算属性保持一致）
					if (this.dateRange[0] && this.dateRange[1]) {
						const recordDate = record.date
						return recordDate >= this.dateRange[0] && recordDate <= this.dateRange[1]
					}

					// 如果没有设置日期范围，使用当前月份作为默认范围
					const currentMonthStart = this.getCurrentMonthStart()
					const currentMonthEnd = this.getCurrentMonthEnd()
					const recordDate = record.date
					return recordDate >= currentMonthStart && recordDate <= currentMonthEnd
				})

				if (workerRecords.length === 0) {
					return {
						workerName,
						totalEarnings: 0,
						totalDays: 0,
						avgDailyQuantity: 0,
						teaPickingDays: 0
					}
				}

				// 计算采茶工作天数
				const teaPickingRecords = workerRecords.filter(record =>
					record.work_mode === 'tea_picking' && record.tea_picking_details)
				const teaPickingDays = teaPickingRecords.length

				// 计算总收入
				const totalEarnings = workerRecords.reduce((sum, record) =>
					sum + parseFloat(record.total_earnings || 0), 0)

				// 计算出勤天数
				const totalDays = workerRecords.length

				// 计算平均日产量（只统计采茶模式）
				const totalQuantity = teaPickingRecords.reduce((sum, record) => {
					// 处理数组格式的tea_picking_details
					if (Array.isArray(record.tea_picking_details)) {
						return sum + record.tea_picking_details.reduce((detailSum, detail) =>
							detailSum + parseFloat(detail.actual_weight || detail.weight || 0), 0)
					}
					// 处理对象格式的tea_picking_details
					return sum + parseFloat(record.tea_picking_details.actual_weight || record.tea_picking_details.weight || 0)
				}, 0)

				const avgDailyQuantity = teaPickingDays > 0 ?
					totalQuantity / teaPickingDays : 0

				return {
					workerName,
					totalEarnings,
					totalDays,
					avgDailyQuantity,
					teaPickingDays
				}
			}).filter(stats => stats.totalDays > 0) // 只包含有数据的工人

			return allWorkersData
		},

		// 获取符合排名条件的工人统计数据（采茶工作天数 > 5天）
		getEligibleWorkersStats() {
			const allWorkersStats = this.getAllWorkersStats()
			// 只有采茶工作天数 > 5天的工人才参与排名
			return allWorkersStats.filter(stats => stats.teaPickingDays > 5)
		},

		getDateRangeLength() {
			if (!this.dateRange[0] || !this.dateRange[1]) return 0
			const start = new Date(this.dateRange[0])
			const end = new Date(this.dateRange[1])
			return Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1
		},

		resetStats() {
			this.workerStats = {
				totalDays: 0,
				totalEarnings: 0,
				avgDailyEarnings: 0,
				totalQuantity: 0,
				attendanceRate: 0
			}
			this.workModeStats = {
				teaPicking: { days: 0, percentage: 0 },
				hourly: { days: 0, percentage: 0 }
			}
			this.earningsStats = {
				maxDaily: 0,
				minDaily: 0
			}
			this.efficiencyStats = {
				teaPicking: {
					totalDays: 0,
					avgQuantity: 0,
					maxQuantity: 0,
					minQuantity: 0,
					stability: '无数据'
				},
				hourly: {
					totalDays: 0,
					avgHours: 0,
					avgHourlyRate: 0,
					overtimeRate: 0
				}
			}
			this.workerRanking = {
				earnings: 1,
				attendance: 1,
				efficiency: 1,
				totalWorkers: 1,
				earningsPercent: 0,
				attendancePercent: 0,
				efficiencyPercent: 0,
				isEligible: false
			}
		}
	}
}

</script>

<style scoped>
.worker-analysis {
	padding: 20rpx;
}

/* 统一选择器面板 */
.unified-selector {
	background-color: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 标题区域 */
.selector-title-section {
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.main-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

/* 选择器内容区域 */
.selector-content {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 选择区域通用样式 */
.worker-selection-area,
.date-selection-area {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.name-input-wrapper {
	display: flex;
	align-items: center;
	gap: 15rpx;
	position: relative;
}

.form-input {
	flex: 1;
	padding: 20rpx 25rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 10rpx;
	font-size: 28rpx;
	background-color: #f8f9fa;
	transition: all 0.3s ease;
}

.form-input:focus {
	border-color: #2e7d32;
	background-color: #fff;
	outline: none;
	box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.1);
}

/* 当建议列表显示时，修改输入框下边框样式 */
.name-input-wrapper.suggestions-active .form-input {
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
	border-bottom-color: transparent;
}

.input-icon:hover {
	background-color: #e0e0e0;
}

/* 姓名建议列表 */
.name-suggestions {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background-color: #fff;
	border: 2rpx solid #e0e0e0;
	border-top: none;
	border-radius: 0 0 8rpx 8rpx;
	max-height: 400rpx;
	overflow-y: auto;
	z-index: 1000;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	margin-top: 2rpx;
}

.suggestion-item {
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.3s ease;
	font-size: 32rpx;
	color: #333;
}

.suggestion-item:last-child {
	border-bottom: none;
}

.suggestion-item:hover {
	background-color: #f8f9fa;
}

/* 姓名建议遮罩层 */
.suggestions-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	background-color: transparent;
}

/* 日期范围选择样式 */
.date-range-inputs {
	display: flex;
	align-items: center;
	gap: 15rpx;
	flex-wrap: wrap;
}

/* 在小屏幕上调整布局 */
@media (max-width: 600rpx) {
	.date-range-inputs {
		flex-direction: column;
		gap: 20rpx;
		align-items: stretch;
	}

	.date-separator {
		text-align: center;
		padding: 10rpx 0;
	}

	.reset-button {
		align-self: center;
		min-width: 20rpx;
	}
}

.date-picker {
	flex: 1;
	padding: 20rpx 25rpx;
	background-color: #f8f9fa;
	border: 2rpx solid #e9ecef;
	border-radius: 10rpx;
	text-align: center;
	transition: all 0.3s ease;
}

.date-picker:hover {
	background-color: #e9ecef;
	border-color: #2e7d32;
}

.date-text {
	font-size: 28rpx;
	color: #2e7d32;
	font-weight: 500;
}

.date-separator {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
	padding: 0 10rpx;
}

/* 回到本月按钮 */
.reset-button {
	padding: 20rpx 25rpx;
	background-color: #2e7d32;
	border: 2rpx solid #2e7d32;
	border-radius: 10rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	white-space: nowrap;
	flex-shrink: 0;
}

.reset-button:hover {
	background-color: #45a049;
	border-color: #45a049;
	transform: translateY(-1rpx);
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.reset-button:active {
	transform: translateY(0);
	box-shadow: 0 2rpx 6rpx rgba(76, 175, 80, 0.3);
}

.reset-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

/* 工人概览 */
.worker-overview {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.overview-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.overview-cards {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.overview-card {
	text-align: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
}

.card-value {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 8rpx;
}

.card-label {
	font-size: 26rpx;
	color: #666;
}

/* 分析部分 */
.attendance-section,
.earnings-section,
.ranking-section {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

/* 工作模式分布 */
.work-mode-distribution {
	margin-bottom: 30rpx;
}

.distribution-title {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.mode-stats {
	display: flex;
	gap: 20rpx;
}

.mode-item {
	flex: 1;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	text-align: center;
}

.mode-name {
	display: block;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.mode-days {
	display: block;
	font-size: 32rpx;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 4rpx;
}

.mode-percentage {
	font-size: 24rpx;
	color: #999;
}



/* 统计网格 */
.earnings-stats {
	margin-bottom: 30rpx;
}

.stats-title {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 15rpx;
}

.stat-item,
.efficiency-item {
	padding: 15rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	text-align: center;
}

.stat-label,
.efficiency-label {
	display: block;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.stat-value,
.efficiency-value {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #2e7d32;
}

/* 排名卡片 */
.ranking-cards {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.ranking-card {
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	text-align: center;
}

.ranking-title {
	display: block;
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.ranking-position {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 8rpx;
}

.ranking-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 5rpx;
}

.ranking-percent {
	font-size: 22rpx;
	color: #2e7d32;
	font-weight: 500;
}

/* 不符合排名条件提示 */
.ranking-ineligible {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx 40rpx;
	text-align: center;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	border: 2rpx dashed #dee2e6;
}

.ineligible-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.ineligible-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #6c757d;
	margin-bottom: 15rpx;
	display: block;
}

.ineligible-desc {
	font-size: 26rpx;
	color: #6c757d;
	margin-bottom: 10rpx;
	line-height: 1.5;
	display: block;
}

.ineligible-suggestion {
	font-size: 24rpx;
	color: #2e7d32;
	font-weight: 500;
	display: block;
}

/* 无数据提示 */
.no-data {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	text-align: center;
}

.no-data-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
}

.no-data-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.no-data-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.no-data-suggestion {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
	line-height: 1.4;
}

/* 调试信息样式 */
.debug-info {
	background-color: #f8f9fa;
	border: 1rpx solid #dee2e6;
	border-radius: 8rpx;
	padding: 20rpx;
	margin: 20rpx;
	font-family: monospace;
}

.debug-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #495057;
	margin-bottom: 15rpx;
	display: block;
}

.debug-subtitle {
	font-size: 24rpx;
	font-weight: 500;
	color: #6c757d;
	margin: 10rpx 0 5rpx 0;
	display: block;
}

.debug-item {
	font-size: 22rpx;
	color: #6c757d;
	margin-bottom: 5rpx;
	display: block;
	line-height: 1.4;
}

/* 姓名库弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background-color: white;
	border-radius: 12rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.modal-close {
	font-size: 40rpx;
	color: #999;
	cursor: pointer;
	padding: 10rpx;
}

.name-list {
	max-height: 60vh;
	overflow-y: auto;
}

.name-item {
	padding: 25rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.name-item:last-child {
	border-bottom: none;
}

.name-item:hover {
	background-color: #f8f9fa;
}

.name-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.name-text {
	font-size: 32rpx;
	color: #333;
}
</style>
