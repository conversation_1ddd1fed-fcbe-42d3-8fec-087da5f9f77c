<template>
	<view class="financial-analysis">
		<!-- 月份选择器 -->
		<view class="month-selector">
			<view class="selector-header">
				<text class="selector-title">财务分析</text>
				<picker mode="date" fields="month" :value="selectedMonth" @change="onMonthChange">
					<view class="month-picker">
						<text class="month-text">{{ formatMonth(selectedMonth) }}</text>
						<text class="picker-arrow">📅</text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 财务概览 -->
		<view class="financial-overview">
			<view class="overview-title">财务概览</view>
			<view class="overview-cards">
				<view class="overview-card tea-picking-expense">
					<text class="card-value">¥{{ $formatCurrency(financialStats.teaPickingExpense) }}</text>
					<text class="card-label">采茶支出</text>
				</view>
				<view class="overview-card labor-expense">
					<text class="card-value">¥{{ $formatCurrency(financialStats.laborExpense) }}</text>
					<text class="card-label">时工支出</text>
				</view>
				<view class="overview-card highlight total-expense">
					<text class="card-value">¥{{ $formatCurrency(financialStats.totalExpense) }}</text>
					<text class="card-label">总支出</text>
				</view>
				<view class="overview-card highlight total-income">
					<text class="card-value">¥{{ $formatCurrency(financialStats.totalIncome) }}</text>
					<text class="card-label">总收入</text>
				</view>
				<view class="overview-card highlight gross-profit" :class="financialStats.grossProfit >= 0 ? 'profit-positive' : 'profit-negative'">
					<text class="card-value">¥{{ $formatCurrency(financialStats.grossProfit) }}</text>
					<text class="card-label">毛利润</text>
				</view>
			</view>
		</view>

		<!-- 收入分析 -->
		<view class="income-section">
			<view class="section-title">收入分析</view>
			<!-- 日收入分析 -->
			<view class="daily-income-analysis">
				<view class="income-stats">
					<view class="stat-item">
						<text class="stat-label">最高日收入</text>
						<text class="stat-value">¥{{ $formatCurrency(dailyIncomeStats.max) }}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">最低日收入</text>
						<text class="stat-value">¥{{ $formatCurrency(dailyIncomeStats.min) }}</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 支出分析 -->
		<view class="expense-section">
			<view class="section-title">支出分析</view>

			<!-- 日均支出分析 -->
			<view class="daily-expense-analysis">
				<view class="expense-stats">
					<view class="stat-item">
						<text class="stat-label">最高日支出</text>
						<text class="stat-value">¥{{ $formatCurrency(dailyExpenseStats.max) }}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">最低日支出</text>
						<text class="stat-value">¥{{ $formatCurrency(dailyExpenseStats.min) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 财务对比分析 -->
		<view class="comparison-section">
			<view class="section-title">财务对比分析</view>
			<view class="comparison-cards">
				<view class="comparison-card">
					<text class="comparison-title">与上月对比</text>
					<view class="comparison-item">
						<text class="comparison-label">总支出变化</text>
						<text class="comparison-value"
							:class="financialComparison.expenseChange >= 0 ? 'negative' : 'positive'">
							{{ financialComparison.expenseChange >= 0 ? '+' : '' }}{{
								financialComparison.expenseChange.toFixed(1) }}%
						</text>
					</view>
					<view class="comparison-item">
						<text class="comparison-label">总收入变化</text>
						<text class="comparison-value"
							:class="financialComparison.incomeChange >= 0 ? 'positive' : 'negative'">
							{{ financialComparison.incomeChange >= 0 ? '+' : '' }}{{
								financialComparison.incomeChange.toFixed(1) }}%
						</text>
					</view>
					<view class="comparison-item">
						<text class="comparison-label">毛利润变化</text>
						<text class="comparison-value"
							:class="financialComparison.profitChange >= 0 ? 'positive' : 'negative'">
							{{ financialComparison.profitChange >= 0 ? '+' : '' }}{{
								financialComparison.profitChange.toFixed(1) }}%
						</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import SalesManager from '../../../utils/salesManager.js'

export default {
	name: 'FinancialAnalysis',

	props: {
		records: {
			type: Array,
			default: () => []
		},
		selectedMonth: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			// 保留一些需要的数据属性
			costEfficiency: {
				teaPicking: {
					totalQuantity: 0,
					costPerKg: 0,
					avgPrice: 0,
					efficiency: 0
				}
			},
			salesManager: null
		}
	},
	computed: {
		teaPickingRecords() {
			return this.records.filter(record => record.work_mode === 'tea_picking')
		},

		hourlyWorkRecords() {
			return this.records.filter(record => record.work_mode === 'hourly')
		},

		// 财务统计数据
		financialStats() {
			// 采茶支出总额
			const teaPickingExpense = this.teaPickingRecords.reduce((sum, record) => {
				return sum + (parseFloat(record.total_earnings) || 0)
			}, 0)

			// 时工支出总额
			const laborExpense = this.hourlyWorkRecords.reduce((sum, record) => {
				return sum + (parseFloat(record.total_earnings) || 0)
			}, 0)

			// 总支出（采茶+时工+其他）
			const totalExpense = teaPickingExpense + laborExpense

			// 总收入（需要从销售记录中获取）
			const totalIncome = this.calculateTotalIncome()

			// 毛利润（总收入-总支出）
			const grossProfit = totalIncome - totalExpense

			return {
				teaPickingExpense,
				laborExpense,
				totalExpense,
				totalIncome,
				grossProfit
			}
		},

		// 日支出统计
		dailyExpenseStats() {
			// 按日期分组计算每日支出
			const dailyExpenses = {}

			this.records.forEach(record => {
				const date = record.date
				if (!dailyExpenses[date]) {
					dailyExpenses[date] = 0
				}
				dailyExpenses[date] += parseFloat(record.total_earnings || 0)
			})

			const expenses = Object.values(dailyExpenses)

			if (expenses.length === 0) {
				return { max: 0, min: 0 }
			}

			return {
				max: Math.max(...expenses),
				min: Math.min(...expenses)
			}
		},

		// 日收入统计
		dailyIncomeStats() {
			// 按日期分组计算每日收入
			const dailyIncomes = this.calculateDailyIncomes()
			const incomes = Object.values(dailyIncomes)

			if (incomes.length === 0) {
				return { max: 0, min: 0 }
			}

			return {
				max: Math.max(...incomes),
				min: Math.min(...incomes)
			}
		},

		// 财务对比分析
		financialComparison() {
			// 获取上月数据进行对比
			const currentMonthData = this.financialStats
			const previousMonthData = this.getPreviousMonthData()

			// 计算变化百分比
			const expenseChange = this.calculatePercentageChange(
				previousMonthData.totalExpense,
				currentMonthData.totalExpense
			)

			const incomeChange = this.calculatePercentageChange(
				previousMonthData.totalIncome,
				currentMonthData.totalIncome
			)

			const profitChange = this.calculatePercentageChange(
				previousMonthData.grossProfit,
				currentMonthData.grossProfit
			)

			return {
				expenseChange,
				incomeChange,
				profitChange
			}
		},

		// 月度支出趋势图表数据
		monthlyExpenseChartData() {
			const monthlyData = this.getMonthlyFinancialData()
			const months = Object.keys(monthlyData).sort()
			const expenseData = months.map(month => monthlyData[month].totalExpense)

			return {
				categories: months.map(month => month.substring(5) + '月'),
				series: [{ name: '支出(元)', data: expenseData }]
			}
		}
	},
	watch: {
		records: {
			handler() {
				// 数据变化时重新计算成本效益分析
				this.calculateCostEfficiency()
			},
			immediate: true
		},
		selectedMonth() {
			console.log('📅 [财务分析] 月份变化:', this.selectedMonth)
			this.$emit('month-change', this.selectedMonth)
			// 月份变化时，强制重新计算财务统计数据
			this.$forceUpdate()
		}
	},
	async mounted() {
		// 初始化销售管理器
		this.salesManager = new SalesManager()
		console.log('📊 [财务分析] 销售管理器已初始化')
	},
	methods: {
		onMonthChange(e) {
			this.$emit('month-change', e.detail.value)
		},

		formatMonth(month) {
			if (!month) return '选择月份'
			const [year, monthNum] = month.split('-')
			return `${year}年${monthNum}月`
		},

		// 计算总收入（使用与收入页面相同的数据源和逻辑）
		calculateTotalIncome() {
			try {
				console.log('💰 [财务分析] 开始计算总收入')

				if (!this.salesManager) {
					console.log('❌ [财务分析] 销售管理器未初始化')
					return 0
				}

				if (!this.selectedMonth) {
					console.log('❌ [财务分析] 未选择月份')
					return 0
				}

				// 计算月份的开始和结束日期
				const startDate = `${this.selectedMonth}-01`
				const [year, month] = this.selectedMonth.split('-')
				const endDate = new Date(parseInt(year), parseInt(month), 0).toISOString().split('T')[0]

				console.log('📅 [财务分析] 计算收入的日期范围:', startDate, '到', endDate)

				// 使用与收入页面相同的方法获取收入记录
				const incomeRecords = this.salesManager.getIncomeRecordsByDateRange(startDate, endDate)
				console.log('📊 [财务分析] 获取到的收入记录数量:', incomeRecords.length)
				console.log('📊 [财务分析] 收入记录详情:', incomeRecords.map(r => ({
					id: r.id,
					date: r.date,
					total_income: r.total_income,
					selling_price: r.selling_price,
					production: r.production,
					isPlaceholder: r.isPlaceholder
				})))

				// 计算总收入（使用与收入页面相同的逻辑）
				const totalIncome = incomeRecords.reduce((sum, record) => {
					if (record.isPlaceholder) {
						console.log('⏭️ [财务分析] 跳过占位符记录:', record.date)
						return sum
					}

					let recordIncome = 0

					// 如果有 total_income 字段，直接使用
					if (record.total_income !== undefined && record.total_income !== null) {
						recordIncome = parseFloat(record.total_income) || 0
						console.log('💰 [财务分析] 使用total_income字段:', record.date, recordIncome)
					} else {
						// 否则使用 selling_price * production 计算
						const price = parseFloat(record.selling_price) || 0
						const production = parseFloat(record.production) || 0
						recordIncome = price * production
						console.log('💰 [财务分析] 计算收入:', record.date, `${price} × ${production} = ${recordIncome}`)
					}

					return sum + recordIncome
				}, 0)

				console.log('💰 [财务分析] 总收入计算结果:', totalIncome)
				return totalIncome

			} catch (error) {
				console.error('❌ [财务分析] 计算总收入失败:', error)
				return 0
			}
		},

		// 计算每日收入（使用与收入页面相同的数据源和逻辑）
		calculateDailyIncomes() {
			try {
				console.log('📊 [财务分析] 开始计算每日收入')

				if (!this.salesManager) {
					console.log('❌ [财务分析] 销售管理器未初始化')
					return {}
				}

				if (!this.selectedMonth) {
					console.log('❌ [财务分析] 未选择月份')
					return {}
				}

				// 计算月份的开始和结束日期
				const startDate = `${this.selectedMonth}-01`
				const [year, month] = this.selectedMonth.split('-')
				const endDate = new Date(parseInt(year), parseInt(month), 0).toISOString().split('T')[0]

				console.log('📅 [财务分析] 计算每日收入的日期范围:', startDate, '到', endDate)

				// 使用与收入页面相同的方法获取收入记录
				const incomeRecords = this.salesManager.getIncomeRecordsByDateRange(startDate, endDate)
				console.log('📊 [财务分析] 获取到的收入记录数量:', incomeRecords.length)

				// 按日期分组计算收入
				const dailyIncomes = {}
				incomeRecords.forEach(record => {
					if (record.isPlaceholder) {
						console.log('⏭️ [财务分析] 跳过占位符记录:', record.date)
						return
					}

					const date = record.date
					if (!dailyIncomes[date]) {
						dailyIncomes[date] = 0
					}

					let recordIncome = 0

					// 如果有 total_income 字段，直接使用
					if (record.total_income !== undefined && record.total_income !== null) {
						recordIncome = parseFloat(record.total_income) || 0
					} else {
						// 否则使用 selling_price * production 计算
						const price = parseFloat(record.selling_price) || 0
						const production = parseFloat(record.production) || 0
						recordIncome = price * production
					}

					dailyIncomes[date] += recordIncome
					console.log('📊 [财务分析] 日期', date, '收入:', recordIncome, '累计:', dailyIncomes[date])
				})

				console.log('📊 [财务分析] 每日收入计算结果:', dailyIncomes)
				return dailyIncomes

			} catch (error) {
				console.error('❌ [财务分析] 计算每日收入失败:', error)
				return {}
			}
		},

		// 获取上月数据
		getPreviousMonthData() {
			try {
				if (!this.selectedMonth) {
					return { totalExpense: 0, totalIncome: 0, grossProfit: 0 }
				}

				// 计算上月日期
				const [year, month] = this.selectedMonth.split('-')
				const currentDate = new Date(parseInt(year), parseInt(month) - 1, 1)
				const previousDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)
				const previousMonth = `${previousDate.getFullYear()}-${String(previousDate.getMonth() + 1).padStart(2, '0')}`

				// 获取上月工作记录
				const allWorkRecords = uni.getStorageSync('workRecords') || []
				const previousMonthWorkRecords = allWorkRecords.filter(record => {
					if (!record.date) return false
					return record.date.startsWith(previousMonth)
				})

				// 计算上月支出
				const totalExpense = previousMonthWorkRecords.reduce((sum, record) => {
					return sum + (parseFloat(record.total_earnings) || 0)
				}, 0)

				// 获取上月销售记录（使用salesManager）
				let totalIncome = 0
				if (this.salesManager) {
					const previousStartDate = `${previousMonth}-01`
					const [prevYear, prevMonth] = previousMonth.split('-')
					const previousEndDate = new Date(parseInt(prevYear), parseInt(prevMonth), 0).toISOString().split('T')[0]

					console.log('📅 [财务分析] 上月日期范围:', previousStartDate, '到', previousEndDate)

					const previousIncomeRecords = this.salesManager.getIncomeRecordsByDateRange(previousStartDate, previousEndDate)
					console.log('📊 [财务分析] 上月收入记录数量:', previousIncomeRecords.length)

					// 计算上月收入
					totalIncome = previousIncomeRecords.reduce((sum, record) => {
						if (record.isPlaceholder) return sum

						let recordIncome = 0
						if (record.total_income !== undefined && record.total_income !== null) {
							recordIncome = parseFloat(record.total_income) || 0
						} else {
							const price = parseFloat(record.selling_price) || 0
							const production = parseFloat(record.production) || 0
							recordIncome = price * production
						}
						return sum + recordIncome
					}, 0)

					console.log('💰 [财务分析] 上月总收入:', totalIncome)
				}

				const grossProfit = totalIncome - totalExpense

				return { totalExpense, totalIncome, grossProfit }
			} catch (error) {
				console.error('获取上月数据失败:', error)
				return { totalExpense: 0, totalIncome: 0, grossProfit: 0 }
			}
		},

		// 计算百分比变化
		calculatePercentageChange(oldValue, newValue) {
			if (oldValue === 0) {
				return newValue > 0 ? 100 : 0
			}
			return ((newValue - oldValue) / oldValue) * 100
		},



		calculateCostEfficiency() {
			// 采茶模式成本效益
			let teaTotalQuantity = 0
			let teaTotalPrice = 0
			let teaRecordCount = 0

			this.teaPickingRecords.forEach(record => {
				if (record.tea_picking_details) {
					const quantity = parseFloat(record.tea_picking_details.weight || 0)
					const price = parseFloat(record.tea_picking_details.price || 0)
					teaTotalQuantity += quantity
					teaTotalPrice += price
					teaRecordCount++
				}
			})

			const teaCost = this.teaPickingRecords.reduce((sum, record) =>
				sum + parseFloat(record.total_earnings || 0), 0)
			const teaCostPerKg = teaTotalQuantity > 0 ? teaCost / teaTotalQuantity : 0
			const teaAvgPrice = teaRecordCount > 0 ? teaTotalPrice / teaRecordCount : 0
			const teaEfficiency = teaAvgPrice > 0 ? (teaAvgPrice / teaCostPerKg * 100) : 0



			this.costEfficiency = {
				teaPicking: {
					totalQuantity: teaTotalQuantity,
					costPerKg: teaCostPerKg,
					avgPrice: teaAvgPrice,
					efficiency: Math.min(100, teaEfficiency).toFixed(1)
				},

			}
		},



		// 获取月度财务数据
		getMonthlyFinancialData() {
			const monthlyData = {}

			this.records.forEach(record => {
				const month = record.date.substring(0, 7) // YYYY-MM
				if (!monthlyData[month]) {
					monthlyData[month] = {
						totalExpense: 0,
						teaPickingExpense: 0,
						hourlyExpense: 0,
						count: 0
					}
				}

				const expense = parseFloat(record.total_earnings || 0)
				monthlyData[month].totalExpense += expense
				monthlyData[month].count++

				if (record.work_mode === 'tea_picking') {
					monthlyData[month].teaPickingExpense += expense
				} else if (record.work_mode === 'hourly') {
					monthlyData[month].hourlyExpense += expense
				}
			})

			return monthlyData
		}

	}
}
</script>

<style scoped>
.financial-analysis {
	padding: 20rpx;
}

/* 月份选择器 */
.month-selector {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.selector-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.selector-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.month-picker {
	display: flex;
	align-items: center;
	padding: 10rpx 20rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
}

.month-text {
	font-size: 32rpx;
	color: #2e7d32;
	margin-right: 10rpx;
}

.picker-arrow {
	font-size: 24rpx;
}

/* 财务概览 */
.financial-overview {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.overview-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.overview-cards {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.overview-card {
	text-align: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	transition: all 0.3s ease;
	border: 2rpx solid transparent;
}

.overview-card.tea-picking-expense {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
}

.overview-card.labor-expense {
	background: linear-gradient(135deg, #fa8c16, #ffa940);
	color: white;
}

.overview-card.highlight {
	background: linear-gradient(135deg, #2e7d32, #45a049);
	color: white;
}

.overview-card.total-expense {
	background: linear-gradient(135deg, #f44336, #e57373);
}

.overview-card.total-income {
	background: linear-gradient(135deg, #1976d2, #42a5f5);
}

.overview-card.gross-profit.profit-positive {
	background: linear-gradient(135deg, #2e7d32, #66bb6a);
}

.overview-card.gross-profit.profit-negative {
	background: linear-gradient(135deg, #d32f2f, #f44336);
}

.overview-card.highlight .card-value,
.overview-card.highlight .card-label,
.overview-card.tea-picking-expense .card-value,
.overview-card.tea-picking-expense .card-label,
.overview-card.labor-expense .card-value,
.overview-card.labor-expense .card-label {
	color: white;
}

.card-value {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #2e7d32;
	margin-bottom: 8rpx;
}

.card-label {
	font-size: 28rpx;
	color: #666;
}

/* 分析部分 */
.expense-section,
.income-section,
.cost-benefit-section,
.comparison-section,
.budget-suggestions {
	background-color: white;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

/* 统计容器 - 参考MonthlyAnalysis.vue */
.daily-expense-analysis,
.daily-income-analysis,
.tea-cost-analysis,
.stats-container {
	margin-bottom: 30rpx;
}

.analysis-title,
.stats-title {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 15rpx;
}

/* 统计网格 - 参考MonthlyAnalysis.vue */
.expense-stats,
.income-stats,
.cost-efficiency-grid,
.stats-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 15rpx;
}

/* 两列布局的统计网格 */
.expense-stats,
.income-stats,
.cost-efficiency-grid {
	grid-template-columns: repeat(2, 1fr);
}

.stat-item,
.efficiency-item {
	padding: 15rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	text-align: center;
}

.stat-label,
.efficiency-label {
	display: block;
	font-size: 28rpx;
	color: #666;
	margin-bottom: 5rpx;
}

.stat-value,
.efficiency-value {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #2e7d32;
}

/* 对比分析 */
.comparison-cards {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.comparison-card {
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
}

.comparison-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.comparison-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	padding-top: 2rpx;
}

.comparison-item:first-of-type {
	margin-top: 8rpx;
}

.comparison-label {
	font-size: 28rpx;
	color: #666;
}

.comparison-value {
	font-size: 28rpx;
	font-weight: 600;
}

.comparison-value.positive {
	color: #2e7d32;
}

.comparison-value.negative {
	color: #f44336;
}
</style>
