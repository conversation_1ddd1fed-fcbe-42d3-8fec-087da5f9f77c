
.detail-page[data-v-ee0efa43] {
	min-height: 100vh;
	background-color: #f5f5f5;
	/* 适配状态栏高度 - 多种兼容方案 */
	padding-top: constant(safe-area-inset-top);
	/* iOS 11.0-11.2 */
	padding-top: env(safe-area-inset-top);
	/* iOS 11.2+ */
	/* 备用方案 */
	padding-top: var(--status-bar-height, 0px);
}

/* 导航栏样式 - 采茶模式绿色主题 */
.nav-bar[data-v-ee0efa43] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.625rem 0.9375rem;
	background: linear-gradient(135deg, #2e7d32 0%, #2E7D32 100%);
	border-bottom: 0.0625rem solid rgba(76, 175, 80, 0.3);
	position: -webkit-sticky;
	position: sticky;
	top: 0;
	z-index: 100;
	box-shadow: 0 0.0625rem 0.25rem rgba(76, 175, 80, 0.2);
	/* 确保导航栏不会被状态栏遮挡 - 多种兼容方案 */
	margin-top: calc(-1 * constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	margin-top: calc(-1 * env(safe-area-inset-top));
	/* iOS 11.2+ */
	margin-top: calc(-1 * var(--status-bar-height, 0px));
	/* 备用方案 */

	padding-top: calc(0.625rem + constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	padding-top: calc(0.625rem + env(safe-area-inset-top));
	/* iOS 11.2+ */
	padding-top: calc(0.625rem + var(--status-bar-height, 0px));
	/* 备用方案 */
}
.nav-left[data-v-ee0efa43] {
	display: flex;
	align-items: center;
	gap: 0.3125rem;
	cursor: pointer;
}
.nav-icon[data-v-ee0efa43] {
	font-size: 1.125rem;
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.nav-text[data-v-ee0efa43] {
	font-size: 1rem;
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.nav-title[data-v-ee0efa43] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.nav-right[data-v-ee0efa43] {
	min-width: 3.125rem;
	text-align: right;
}
.edit-btn[data-v-ee0efa43] {
	padding: 0.375rem 0.75rem;
	background-color: rgba(255, 255, 255, 0.2);
	color: white;
	border: 0.03125rem solid rgba(255, 255, 255, 0.3);
	border-radius: 0.625rem;
	font-size: 0.875rem;
	cursor: pointer;
	-webkit-backdrop-filter: blur(0.3125rem);
	        backdrop-filter: blur(0.3125rem);
}
.edit-btn[data-v-ee0efa43]:active {
	background-color: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

/* 编辑模式按钮组 */
.edit-actions[data-v-ee0efa43] {
	display: flex;
	gap: 0.46875rem;
}
.save-btn[data-v-ee0efa43],
.cancel-btn[data-v-ee0efa43] {
	padding: 0.375rem 0.75rem;
	border-radius: 0.625rem;
	font-size: 0.875rem;
	cursor: pointer;
	transition: all 0.3s ease;
}
.save-btn[data-v-ee0efa43] {
	background-color: #2e7d32;
	color: white;
}
.save-btn.disabled[data-v-ee0efa43] {
	background-color: #ccc;
	cursor: not-allowed;
}
.save-btn[data-v-ee0efa43]:not(.disabled):active {
	background-color: #45a049;
}
.cancel-btn[data-v-ee0efa43] {
	background-color: #f44336;
	color: white;
}
.cancel-btn[data-v-ee0efa43]:active {
	background-color: #d32f2f;
}

/* 编辑表单样式 */
.edit-input[data-v-ee0efa43] {
	padding: 0.5rem 0.625rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.25rem;
	font-size: 1rem;
	color: #333;
	background-color: #fff;
	transition: border-color 0.3s ease;
}
.edit-input[data-v-ee0efa43]:focus {
	border-color: #2e7d32;
	outline: none;
}
.number-input[data-v-ee0efa43] {
	text-align: right;
}
.edit-picker[data-v-ee0efa43] {
	padding: 0.5rem 0.625rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.25rem;
	background-color: #fff;
	cursor: pointer;
}
.picker-text[data-v-ee0efa43] {
	font-size: 1rem;
	color: #333;
}
.time-picker[data-v-ee0efa43] {
	padding: 0.25rem 0.5rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.1875rem;
	background-color: #fff;
	font-size: 0.8125rem;
}

/* 详情卡片头部 */
.header-actions[data-v-ee0efa43] {
	display: flex;
	align-items: center;
	gap: 0.625rem;
}
.add-detail-btn[data-v-ee0efa43] {
	padding: 0.25rem 0.5rem;
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 0.46875rem;
	font-size: 0.75rem;
	cursor: pointer;
}
.add-detail-btn[data-v-ee0efa43]:active {
	background-color: rgba(255, 255, 255, 0.3);
}

/* 详情项头部编辑 */
.edit-header-actions[data-v-ee0efa43] {
	display: flex;
	align-items: center;
	gap: 0.46875rem;
}
.delete-detail-btn[data-v-ee0efa43] {
	padding: 0.1875rem 0.375rem;
	background-color: #f44336;
	color: white;
	border-radius: 0.375rem;
	font-size: 0.6875rem;
	cursor: pointer;
}
.delete-detail-btn[data-v-ee0efa43]:active {
	background-color: #d32f2f;
}

/* 加载和错误状态 */
.loading-container[data-v-ee0efa43],
.error-container[data-v-ee0efa43] {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 3.125rem 1.25rem;
	text-align: center;
}
.loading-text[data-v-ee0efa43] {
	font-size: 1rem;
	color: #666;
}
.error-icon[data-v-ee0efa43] {
	font-size: 2.5rem;
	margin-bottom: 0.625rem;
}
.error-text[data-v-ee0efa43] {
	font-size: 1rem;
	color: #666;
	margin-bottom: 1.25rem;
}
.back-btn[data-v-ee0efa43] {
	padding: 0.625rem 1.25rem;
	background-color: #2e7d32;
	color: white;
	border: none;
	border-radius: 0.78125rem;
	font-size: 1rem;
}

/* 详情内容 */
.detail-content[data-v-ee0efa43] {
	padding: 0.625rem;
}

/* 卡片通用样式 */
.info-card[data-v-ee0efa43],
.detail-card[data-v-ee0efa43],
.summary-card[data-v-ee0efa43] {
	background-color: #fff;
	border-radius: 0.46875rem;
	margin-bottom: 0.625rem;
	box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1);
	overflow: hidden;
}
.card-header[data-v-ee0efa43] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.9375rem;
	background: linear-gradient(135deg, #2e7d32 0%, #45a049 100%);
	color: white;
}
.card-title[data-v-ee0efa43] {
	font-size: 1rem;
	font-weight: 600;
}
.work-mode-badge[data-v-ee0efa43] {
	padding: 0.25rem 0.5rem;
	border-radius: 0.625rem;
	font-size: 0.75rem;
	background-color: rgba(255, 255, 255, 0.2);
}
.detail-count[data-v-ee0efa43] {
	font-size: 0.8125rem;
	opacity: 0.9;
}

/* 基本信息网格 */
.info-grid[data-v-ee0efa43] {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.9375rem;
	padding: 0.9375rem;
}
.info-item[data-v-ee0efa43] {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
}
.info-label[data-v-ee0efa43] {
	font-size: 0.8125rem;
	color: #666;
	font-weight: 500;
}
.info-value[data-v-ee0efa43] {
	font-size: 1rem;
	color: #333;
	font-weight: 600;
}
.info-value.earnings[data-v-ee0efa43] {
	color: #2e7d32;
	font-size: 1.125rem;
}

/* 详情列表 */
.detail-list[data-v-ee0efa43] {
	padding: 0 0.9375rem 0.9375rem;
}
.detail-item[data-v-ee0efa43] {
	border: 0.0625rem solid #f0f0f0;
	border-radius: 0.375rem;
	margin-bottom: 0.625rem;
	overflow: hidden;
}
.detail-item[data-v-ee0efa43]:last-child {
	margin-bottom: 0;
}
.detail-header[data-v-ee0efa43] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.625rem 0.75rem;
	background-color: #f8f9fa;
	border-bottom: 0.0625rem solid #f0f0f0;
}
.detail-index[data-v-ee0efa43] {
	font-size: 0.875rem;
	font-weight: 600;
	color: #2e7d32;
}
.detail-time[data-v-ee0efa43] {
	font-size: 0.8125rem;
	color: #666;
	padding: 0.1875rem 0.375rem;
	background-color: #e8f5e8;
	border-radius: 0.375rem;
}
.detail-grid[data-v-ee0efa43] {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.625rem;
	padding: 0.75rem;
}
.detail-field[data-v-ee0efa43] {
	display: flex;
	flex-direction: column;
	gap: 0.1875rem;
}
.field-label[data-v-ee0efa43] {
	font-size: 0.75rem;
	color: #666;
}
.field-value[data-v-ee0efa43] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 500;
}
.field-value.earnings[data-v-ee0efa43] {
	color: #2e7d32;
	font-weight: 600;
}

/* 统计汇总网格 */
.summary-grid[data-v-ee0efa43] {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.9375rem;
	padding: 0.9375rem;
}
.summary-item[data-v-ee0efa43] {
	display: flex;
	flex-direction: column;
	gap: 0.25rem;
	text-align: center;
	padding: 0.625rem;
	background-color: #f8f9fa;
	border-radius: 0.375rem;
}
.summary-label[data-v-ee0efa43] {
	font-size: 0.8125rem;
	color: #666;
}
.summary-value[data-v-ee0efa43] {
	font-size: 1rem;
	color: #333;
	font-weight: 600;
}
.summary-value.earnings[data-v-ee0efa43] {
	color: #2e7d32;
	font-size: 1.125rem;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.info-grid[data-v-ee0efa43],
	.detail-grid[data-v-ee0efa43],
	.summary-grid[data-v-ee0efa43] {
		grid-template-columns: 1fr;
		gap: 0.625rem;
}
.nav-title[data-v-ee0efa43] {
		font-size: 1rem;
}
.edit-btn[data-v-ee0efa43] {
		font-size: 0.8125rem;
		padding: 0.3125rem 0.625rem;
}
}

/* 月报模式青色主题 */
.monthly-theme .nav-bar[data-v-ee0efa43] {
	background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
	border-bottom: 0.0625rem solid rgba(0, 188, 212, 0.3);
	box-shadow: 0 0.0625rem 0.25rem rgba(0, 188, 212, 0.2);
}
.monthly-theme .nav-icon[data-v-ee0efa43] {
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.monthly-theme .nav-text[data-v-ee0efa43] {
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.monthly-theme .nav-title[data-v-ee0efa43] {
	color: #ffffff;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.monthly-theme .edit-btn[data-v-ee0efa43] {
	background-color: rgba(255, 255, 255, 0.2);
	color: white;
	border: 0.03125rem solid rgba(255, 255, 255, 0.3);
}
.monthly-theme .edit-btn[data-v-ee0efa43]:active {
	background-color: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}
.monthly-theme .save-btn[data-v-ee0efa43] {
	background-color: #00BCD4;
}
.monthly-theme .save-btn[data-v-ee0efa43]:not(.disabled):active {
	background-color: #0097A7;
}
.monthly-theme .back-btn[data-v-ee0efa43] {
	background-color: #00BCD4;
}
.monthly-theme .card-header[data-v-ee0efa43] {
	background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
}
.monthly-theme .info-value.earnings[data-v-ee0efa43],
.monthly-theme .field-value.earnings[data-v-ee0efa43],
.monthly-theme .summary-value.earnings[data-v-ee0efa43] {
	color: #00BCD4;
}
.monthly-theme .detail-title[data-v-ee0efa43] {
	color: #00BCD4;
}
.monthly-theme .edit-input[data-v-ee0efa43]:focus {
	border-color: #00BCD4;
}

/* 扣水量编辑容器 */
.moisture-edit-container[data-v-ee0efa43] {
	display: flex;
	align-items: center;
	gap: 0.15625rem;
}
.moisture-rate-input[data-v-ee0efa43] {
	width: 2.5rem;
	text-align: center;
	padding: 0.375rem 0.25rem;
}
.moisture-unit[data-v-ee0efa43] {
	font-size: 1rem;
	color: #333;
	font-weight: 500;
	white-space: nowrap;
}
