<template>
	<view class="detail-page" :class="{ 'monthly-theme': source === 'monthly' }">
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<text class="nav-icon">←</text>
				<text class="nav-text">返回</text>
			</view>
			<view class="nav-title">时工记录详情</view>
			<view class="nav-right">
				<!-- 查看模式：显示编辑按钮（月报模式不显示） -->
				<text class="edit-btn" v-if="isAdmin && recordFound && !isEditing"
					@click="enterEditMode">编辑</text>

				<!-- 编辑模式：显示保存和取消按钮 -->
				<view v-if="isEditing" class="edit-actions">
					<text class="save-btn" @click="saveChanges" :class="{ disabled: saving }">
						{{ saving ? '保存中...' : '保存' }}
					</text>
					<text class="cancel-btn" @click="cancelEdit">取消</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 记录未找到 -->
		<view class="error-container" v-else-if="!recordFound">
			<text class="error-icon">❌</text>
			<text class="error-text">记录不存在或已被删除</text>
			<button class="back-btn" @click="goBack">返回列表</button>
		</view>



		<!-- 记录详情 -->
		<view class="detail-content" v-else>
			<!-- 基本信息卡片 -->
			<view class="info-card">
				<view class="card-header">
					<text class="card-title">📋 基本信息</text>
					<view class="work-mode-badge hourly">
						<text>⏰ 时工</text>
					</view>
				</view>
				<view class="info-grid">
					<view class="info-item">
						<text class="info-label">工人姓名</text>
						<!-- 查看模式 -->
						<text v-if="!isEditing" class="info-value">{{ recordData.worker_name }}</text>
						<!-- 编辑模式 -->
						<input v-else v-model="editData.worker_name" class="edit-input" placeholder="请输入工人姓名"
							maxlength="20" />
					</view>
					<view class="info-item">
						<text class="info-label">工作日期</text>
						<!-- 查看模式 -->
						<text v-if="!isEditing" class="info-value">{{ $formatDate(recordData.date) }}</text>
						<!-- 编辑模式 -->
						<picker v-else mode="date" :value="editData.date" @change="onDateChange" class="edit-picker">
							<view class="picker-text">{{ editData.date || '请选择日期' }}</view>
						</picker>
					</view>
					<view class="info-item">
						<text class="info-label">总工钱</text>
						<text class="info-value earnings">¥{{ $formatCurrency(calculateTotalEarnings()) }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">记录时间</text>
						<text class="info-value">{{ $formatDate(recordData.created_at, 'YYYY-MM-DD HH:mm') }}</text>
					</view>
				</view>
			</view>

			<!-- 统计汇总卡片 -->
			<view class="summary-card">
				<view class="card-header">
					<text class="card-title">📊 统计汇总</text>
				</view>
				<view class="summary-grid">
					<view class="summary-item">
						<text class="summary-label">工作模式</text>
						<text class="summary-value">{{ getWorkModeText() }}</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">总工时</text>
						<text class="summary-value">{{ getTotalHours() }}小时</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">平均时薪</text>
						<text class="summary-value">¥{{ getAverageRate() }}/时</text>
					</view>
					<view class="summary-item">
						<text class="summary-label">总工钱</text>
						<text class="summary-value earnings">¥{{ $formatCurrency(recordData.total_earnings) }}</text>
					</view>
				</view>
			</view>

			<!-- 时工详情卡片 -->
			<view class="detail-card">
				<view class="card-header">
					<text class="card-title">⏰ 时工详情</text>
					<text class="detail-mode">{{ getWorkModeText() }}</text>
				</view>

				<view class="hourly-content">
					<!-- 简化模式 -->
					<view v-if="!getCurrentHourlyDetails().is_detail_mode" class="simple-mode">
						<view class="mode-title">简化模式</view>
						<view class="simple-grid">
							<view class="simple-item">
								<text class="simple-label">工作时长</text>
								<!-- 查看模式 -->
								<text v-if="!isEditing" class="simple-value">{{
									getCurrentHourlyDetails().simple_mode.work_hours }}小时</text>
								<!-- 编辑模式 -->
								<input v-else v-model="editData.hourly_work_details.simple_mode.work_hours"
									class="edit-input number-input" type="digit" placeholder="0.00"
									@input="onSimpleHoursChange" />
							</view>
							<view class="simple-item">
								<text class="simple-label">时薪</text>
								<!-- 查看模式 -->
								<text v-if="!isEditing" class="simple-value">¥{{
									$formatCurrency(getCurrentHourlyDetails().simple_mode.hourly_rate) }}/时</text>
								<!-- 编辑模式 -->
								<input v-else v-model="editData.hourly_work_details.simple_mode.hourly_rate"
									class="edit-input number-input" type="digit" placeholder="0.00"
									@input="onSimpleRateChange" />
							</view>
							<view class="simple-item">
								<text class="simple-label">总工钱</text>
								<text class="simple-value earnings">¥{{ $formatCurrency(calculateTotalEarnings())
								}}</text>
							</view>
						</view>
					</view>

					<!-- 详细模式 -->
					<view v-else class="detail-mode">
						<view class="mode-title">详细模式</view>

						<!-- 上午时段 -->
						<view class="time-period" v-if="shouldShowPeriod('morning')">
							<view class="period-header">
								<text class="period-title">🌅 上午</text>
							</view>
							<view class="period-grid">
								<view class="period-item">
									<text class="period-label">工作时长</text>
									<!-- 查看模式 -->
									<text v-if="!isEditing" class="period-value">{{
										getCurrentHourlyDetails().detail_mode.morning.work_hours }}小时</text>
									<!-- 编辑模式 -->
									<input v-else v-model="editData.hourly_work_details.detail_mode.morning.work_hours"
										class="edit-input number-input" type="digit" placeholder="0.00"
										@input="onDetailHoursChange('morning')" />
								</view>
								<view class="period-item">
									<text class="period-label">时薪</text>
									<!-- 查看模式 -->
									<text v-if="!isEditing" class="period-value">¥{{
										$formatCurrency(getCurrentHourlyDetails().detail_mode.morning.hourly_rate)
									}}/时</text>
									<!-- 编辑模式 -->
									<input v-else v-model="editData.hourly_work_details.detail_mode.morning.hourly_rate"
										class="edit-input number-input" type="digit" placeholder="0.00"
										@input="onDetailRateChange('morning')" />
								</view>
								<view class="period-item">
									<text class="period-label">工钱</text>
									<text class="period-value earnings">¥{{
										$formatCurrency(calculatePeriodEarnings('morning')) }}</text>
								</view>
							</view>
						</view>

						<!-- 下午时段 -->
						<view class="time-period" v-if="shouldShowPeriod('afternoon')">
							<view class="period-header">
								<text class="period-title">🌇 下午</text>
							</view>
							<view class="period-grid">
								<view class="period-item">
									<text class="period-label">工作时长</text>
									<!-- 查看模式 -->
									<text v-if="!isEditing" class="period-value">{{
										getCurrentHourlyDetails().detail_mode.afternoon.work_hours }}小时</text>
									<!-- 编辑模式 -->
									<input v-else
										v-model="editData.hourly_work_details.detail_mode.afternoon.work_hours"
										class="edit-input number-input" type="digit" placeholder="0.00"
										@input="onDetailHoursChange('afternoon')" />
								</view>
								<view class="period-item">
									<text class="period-label">时薪</text>
									<!-- 查看模式 -->
									<text v-if="!isEditing" class="period-value">¥{{
										$formatCurrency(getCurrentHourlyDetails().detail_mode.afternoon.hourly_rate)
									}}/时</text>
									<!-- 编辑模式 -->
									<input v-else
										v-model="editData.hourly_work_details.detail_mode.afternoon.hourly_rate"
										class="edit-input number-input" type="digit" placeholder="0.00"
										@input="onDetailRateChange('afternoon')" />
								</view>
								<view class="period-item">
									<text class="period-label">工钱</text>
									<text class="period-value earnings">¥{{
										$formatCurrency(calculatePeriodEarnings('afternoon')) }}</text>
								</view>
							</view>
						</view>

						<!-- 加班时段 -->
						<view class="time-period" v-if="shouldShowPeriod('overtime')">
							<view class="period-header">
								<text class="period-title">🌙 加班</text>
							</view>
							<view class="period-grid">
								<view class="period-item">
									<text class="period-label">工作时长</text>
									<!-- 查看模式 -->
									<text v-if="!isEditing" class="period-value">{{
										getCurrentHourlyDetails().detail_mode.overtime.work_hours }}小时</text>
									<!-- 编辑模式 -->
									<input v-else v-model="editData.hourly_work_details.detail_mode.overtime.work_hours"
										class="edit-input number-input" type="digit" placeholder="0.00"
										@input="onDetailHoursChange('overtime')" />
								</view>
								<view class="period-item">
									<text class="period-label">时薪</text>
									<!-- 查看模式 -->
									<text v-if="!isEditing" class="period-value">¥{{
										$formatCurrency(getCurrentHourlyDetails().detail_mode.overtime.hourly_rate)
									}}/时</text>
									<!-- 编辑模式 -->
									<input v-else
										v-model="editData.hourly_work_details.detail_mode.overtime.hourly_rate"
										class="edit-input number-input" type="digit" placeholder="0.00"
										@input="onDetailRateChange('overtime')" />
								</view>
								<view class="period-item">
									<text class="period-label">工钱</text>
									<text class="period-value earnings">¥{{
										$formatCurrency(calculatePeriodEarnings('overtime')) }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
	name: 'HourlyRecordDetailPage',
	data() {
		return {
			recordId: null,
			loading: true,
			recordFound: false,
			recordData: {},
			hourlyDetails: {},
			// 来源标识
			source: 'daily', // 'daily' 或 'monthly'
			// 编辑状态
			isEditing: false,
			editData: {
				worker_name: '',
				date: '',
				hourly_work_details: {}
			},
			originalData: {},
			// 保存状态
			saving: false
		}
	},
	computed: {
		...mapGetters('user', ['isAdmin'])
	},
	onLoad(options) {
		// 初始化用户状态
		this.$store.dispatch('user/initUserState')

		// 适配状态栏高度
		this.adaptStatusBar()

		// 设置来源
		if (options.source) {
			this.source = options.source
		}

		if (options.id) {
			this.recordId = options.id
			this.loadRecord()
		} else {
			this.loading = false
			this.recordFound = false
		}
	},
	methods: {
		// 适配状态栏高度
		adaptStatusBar() {
			try {
				const systemInfo = uni.getSystemInfoSync()
				const statusBarHeight = systemInfo.statusBarHeight || 0

				console.log('状态栏高度:', statusBarHeight)

				// 设置 CSS 变量作为备用方案
				if (statusBarHeight > 0) {
					// #ifdef H5
					if (typeof document !== 'undefined') {
						document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight}px`)

						// 检查是否支持 safe-area-inset
						const supportsSafeArea = CSS.supports('padding-top', 'env(safe-area-inset-top)') ||
							CSS.supports('padding-top', 'constant(safe-area-inset-top)')

						if (!supportsSafeArea) {
							// 使用 JavaScript 动态设置
							this.$nextTick(() => {
								const detailPage = document.querySelector('.detail-page')
								if (detailPage) {
									detailPage.style.paddingTop = `${statusBarHeight}px`
								}

								const navBar = document.querySelector('.nav-bar')
								if (navBar) {
									navBar.style.marginTop = `-${statusBarHeight}px`
									navBar.style.paddingTop = `${statusBarHeight + 10}px` // 10px 对应原来的 20rpx
								}
							})
						}
					}
					// #endif

					// #ifdef APP-PLUS || MP
					// 在 App 和小程序中，通过设置页面样式
					this.$nextTick(() => {
						const pages = getCurrentPages()
						const currentPage = pages[pages.length - 1]
						if (currentPage && currentPage.$vm) {
							currentPage.$vm.$el.style.paddingTop = `${statusBarHeight}px`
						}
					})
					// #endif
				}
			} catch (error) {
				console.log('状态栏适配失败:', error)
			}
		},

		async loadRecord() {
			try {
				this.loading = true

				const records = uni.getStorageSync('workRecords') || []
				const record = records.find(r => r.id == this.recordId)

				if (record && record.work_mode === 'hourly') {
					this.recordFound = true
					this.recordData = record
					this.hourlyDetails = record.hourly_work_details || {}
				} else {
					this.recordFound = false
				}
			} catch (error) {
				console.error('加载记录失败:', error)
				this.recordFound = false
				uni.showToast({
					title: '加载失败',
					icon: 'error'
				})
			} finally {
				this.loading = false
			}
		},

		// 表单处理方法
		getCurrentHourlyDetails() {
			return this.isEditing ? this.editData.hourly_work_details : this.hourlyDetails
		},

		onDateChange(e) {
			this.editData.date = e.detail.value
		},

		onSimpleHoursChange() {
			// 简化模式工时变化时自动重新计算
		},

		onSimpleRateChange() {
			// 简化模式时薪变化时自动重新计算
		},

		onDetailHoursChange() {
			// 详细模式工时变化时自动重新计算
		},

		onDetailRateChange() {
			// 详细模式时薪变化时自动重新计算
		},

		shouldShowPeriod(period) {
			const details = this.getCurrentHourlyDetails()
			if (!details.is_detail_mode) return false

			const periodData = details.detail_mode[period]
			return periodData && (this.isEditing || parseFloat(periodData.work_hours) > 0)
		},

		// 计算方法
		calculatePeriodEarnings(period) {
			const details = this.getCurrentHourlyDetails()
			if (!details.is_detail_mode) return '0.00'

			const periodData = details.detail_mode[period]
			if (!periodData) return '0.00'

			const hours = parseFloat(periodData.work_hours) || 0
			const rate = parseFloat(periodData.hourly_rate) || 0
			return (hours * rate).toFixed(2)
		},

		calculateTotalEarnings() {
			if (!this.isEditing) {
				return this.recordData.total_earnings
			}

			const details = this.editData.hourly_work_details
			if (!details.is_detail_mode) {
				// 简化模式
				const hours = parseFloat(details.simple_mode.work_hours) || 0
				const rate = parseFloat(details.simple_mode.hourly_rate) || 0
				return (hours * rate).toFixed(2)
			} else {
				// 详细模式
				const periods = ['morning', 'afternoon', 'overtime']
				return periods.reduce((total, period) => {
					return total + parseFloat(this.calculatePeriodEarnings(period))
				}, 0).toFixed(2)
			}
		},

		// 统计方法
		getWorkModeText() {
			const details = this.getCurrentHourlyDetails()
			return details.is_detail_mode ? '详细模式' : '简化模式'
		},

		getTotalHours() {
			const details = this.getCurrentHourlyDetails()
			if (!details.is_detail_mode) {
				return (parseFloat(details.simple_mode?.work_hours) || 0).toFixed(2)
			} else {
				const morning = parseFloat(details.detail_mode?.morning?.work_hours) || 0
				const afternoon = parseFloat(details.detail_mode?.afternoon?.work_hours) || 0
				const overtime = parseFloat(details.detail_mode?.overtime?.work_hours) || 0
				return (morning + afternoon + overtime).toFixed(2)
			}
		},

		getAverageRate() {
			const totalHours = parseFloat(this.getTotalHours())
			if (totalHours === 0) return '0.00'

			const totalEarnings = this.isEditing ? this.calculateTotalEarnings() : this.recordData.total_earnings
			return (parseFloat(totalEarnings) / totalHours).toFixed(2)
		},



		// 编辑模式相关方法
		enterEditMode() {
			this.isEditing = true
			// 深拷贝原始数据用于取消操作
			this.originalData = JSON.parse(JSON.stringify(this.recordData))
			// 初始化编辑数据
			this.editData = {
				worker_name: this.recordData.worker_name,
				date: this.recordData.date,
				hourly_work_details: JSON.parse(JSON.stringify(this.hourlyDetails))
			}
		},

		cancelEdit() {
			this.isEditing = false
			// 恢复原始数据
			this.recordData = JSON.parse(JSON.stringify(this.originalData))
			this.hourlyDetails = this.recordData.hourly_work_details || {}
			this.editData = {}
			this.originalData = {}
		},

		async saveChanges() {
			if (this.saving) return

			try {
				// 验证数据
				if (!this.validateEditData()) {
					return
				}

				this.saving = true

				// 计算总工钱
				const totalEarnings = this.calculateTotalEarnings()

				// 构建更新后的记录数据
				const updatedRecord = {
					...this.recordData,
					worker_name: this.editData.worker_name,
					date: this.editData.date,
					total_earnings: totalEarnings,
					hourly_work_details: this.editData.hourly_work_details,
					updated_at: new Date().toISOString()
				}

				// 更新本地存储
				const records = uni.getStorageSync('workRecords') || []
				const recordIndex = records.findIndex(r => r.id == this.recordId)

				if (recordIndex !== -1) {
					// 检测收入是否发生变化
					const originalEarnings = parseFloat(this.originalData.total_earnings || 0)
					const newEarnings = parseFloat(updatedRecord.total_earnings || 0)
					const earningsChanged = Math.abs(originalEarnings - newEarnings) > 0.01

					records[recordIndex] = updatedRecord
					uni.setStorageSync('workRecords', records)

					// 更新页面数据
					this.recordData = updatedRecord
					this.hourlyDetails = this.editData.hourly_work_details

					// 退出编辑模式
					this.isEditing = false
					this.editData = {}
					this.originalData = {}

					// 触发收入页面数据更新事件
					console.log('📡 [时工记录详情] 工作记录修改完成，发送updateIncomeRecord事件')
					console.log('📡 [时工记录详情] 修改的记录:', {
						id: updatedRecord.id,
						date: updatedRecord.date,
						worker_name: updatedRecord.worker_name,
						total_earnings: updatedRecord.total_earnings,
						earningsChanged: earningsChanged,
						originalEarnings: originalEarnings,
						newEarnings: newEarnings
					})

					// 添加延迟确保事件监听器已注册
					setTimeout(() => {
						uni.$emit('updateIncomeRecord', {
							type: 'workRecordUpdated',
							updatedRecord: updatedRecord,
							earningsChanged: earningsChanged,
							originalEarnings: originalEarnings,
							newEarnings: newEarnings,
							timestamp: new Date().toISOString()
						})

						console.log('📡 [时工记录详情] updateIncomeRecord事件已发送（延迟发送）')
					}, 100)

					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
				} else {
					throw new Error('记录不存在')
				}
			} catch (error) {
				console.error('保存失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'error'
				})
			} finally {
				this.saving = false
			}
		},

		// 数据验证
		validateEditData() {
			if (!this.editData.worker_name.trim()) {
				uni.showToast({
					title: '请输入工人姓名',
					icon: 'error'
				})
				return false
			}

			if (!this.editData.date) {
				uni.showToast({
					title: '请选择工作日期',
					icon: 'error'
				})
				return false
			}

			// 验证时工详情
			const details = this.editData.hourly_work_details
			if (!details.is_detail_mode) {
				// 简化模式验证
				if (!details.simple_mode.work_hours || parseFloat(details.simple_mode.work_hours) <= 0) {
					uni.showToast({
						title: '工作时长必须大于0',
						icon: 'error'
					})
					return false
				}

				if (!details.simple_mode.hourly_rate || parseFloat(details.simple_mode.hourly_rate) <= 0) {
					uni.showToast({
						title: '时薪必须大于0',
						icon: 'error'
					})
					return false
				}
			} else {
				// 详细模式验证
				const periods = ['morning', 'afternoon', 'overtime']
				let hasValidPeriod = false

				for (const period of periods) {
					const periodData = details.detail_mode[period]
					if (periodData && parseFloat(periodData.work_hours) > 0) {
						hasValidPeriod = true
						if (!periodData.hourly_rate || parseFloat(periodData.hourly_rate) <= 0) {
							uni.showToast({
								title: `${this.getPeriodName(period)}时薪必须大于0`,
								icon: 'error'
							})
							return false
						}
					}
				}

				if (!hasValidPeriod) {
					uni.showToast({
						title: '至少需要一个时段有工作时长',
						icon: 'error'
					})
					return false
				}
			}

			return true
		},

		getPeriodName(period) {
			const names = { morning: '上午', afternoon: '下午', overtime: '加班' }
			return names[period] || period
		},

		// 操作方法
		goBack() {
			if (this.isEditing) {
				uni.showModal({
					title: '提示',
					content: '您有未保存的更改，确定要离开吗？',
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack()
						}
					}
				})
			} else {
				uni.navigateBack()
			}
		}
	}
}
</script>

<style scoped>
.detail-page {
	min-height: 100vh;
	background-color: #f5f5f5;
	/* 适配状态栏高度 - 多种兼容方案 */
	padding-top: constant(safe-area-inset-top);
	/* iOS 11.0-11.2 */
	padding-top: env(safe-area-inset-top);
	/* iOS 11.2+ */
	/* 备用方案 */
	padding-top: var(--status-bar-height, 0px);
}

/* 导航栏样式 - 时工模式蓝色主题 */
.nav-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background: linear-gradient(135deg, #2196F3 0%, #1565C0 100%);
	border-bottom: 2rpx solid rgba(33, 150, 243, 0.3);
	position: sticky;
	top: 0;
	z-index: 100;
	box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.2);
	/* 确保导航栏不会被状态栏遮挡 - 多种兼容方案 */
	margin-top: calc(-1 * constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	margin-top: calc(-1 * env(safe-area-inset-top));
	/* iOS 11.2+ */
	margin-top: calc(-1 * var(--status-bar-height, 0px));
	/* 备用方案 */

	padding-top: calc(20rpx + constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	padding-top: calc(20rpx + env(safe-area-inset-top));
	/* iOS 11.2+ */
	padding-top: calc(20rpx + var(--status-bar-height, 0px));
	/* 备用方案 */
}

.nav-left {
	display: flex;
	align-items: center;
	gap: 10rpx;
	cursor: pointer;
}

.nav-icon {
	font-size: 36rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.nav-text {
	font-size: 32rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.nav-right {
	min-width: 100rpx;
	text-align: right;
}

.edit-btn {
	padding: 12rpx 24rpx;
	background-color: rgba(255, 255, 255, 0.2);
	color: white;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	border-radius: 20rpx;
	font-size: 28rpx;
	cursor: pointer;
	backdrop-filter: blur(10rpx);
}

.edit-btn:active {
	background-color: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

/* 编辑模式按钮组 */
.edit-actions {
	display: flex;
	gap: 15rpx;
}

.save-btn,
.cancel-btn {
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.save-btn {
	background-color: #f24251;
	color: white;
}

.save-btn.disabled {
	background-color: #ccc;
	cursor: not-allowed;
}

.save-btn:not(.disabled):active {
	background-color: #e11919;
}

.cancel-btn {
	background-color: #2545d4;
	color: white;
}

.cancel-btn:active {
	background-color: #423d3d;
}

/* 编辑表单样式 */
.edit-input {
	padding: 16rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 32rpx;
	color: #333;
	background-color: #fff;
	transition: border-color 0.3s ease;
}

.edit-input:focus {
	border-color: #2196F3;
	outline: none;
}

.number-input {
	text-align: right;
}

.edit-picker {
	padding: 16rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	background-color: #fff;
	cursor: pointer;
}

.picker-text {
	font-size: 32rpx;
	color: #333;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 40rpx;
	text-align: center;
}

.loading-text {
	font-size: 32rpx;
	color: #666;
}

.error-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.error-text {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 40rpx;
}

.back-btn {
	padding: 20rpx 40rpx;
	background-color: #2196F3;
	color: white;
	border: none;
	border-radius: 25rpx;
	font-size: 32rpx;
}

/* 详情内容 */
.detail-content {
	padding: 20rpx;
}

/* 卡片通用样式 */
.info-card,
.detail-card,
.summary-card {
	background-color: #fff;
	border-radius: 15rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
	color: white;
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
}

.work-mode-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	background-color: rgba(255, 255, 255, 0.2);
}

.detail-mode {
	font-size: 26rpx;
	opacity: 0.9;
}

/* 基本信息网格 */
.info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30rpx;
	padding: 30rpx;
}

.info-item {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.info-label {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
}

.info-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

.info-value.earnings {
	color: #2196F3;
	font-size: 36rpx;
}

/* 时工内容 */
.hourly-content {
	padding: 30rpx;
}

.mode-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	text-align: center;
}

/* 简化模式 */
.simple-grid {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 20rpx;
}

.simple-item {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	text-align: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.simple-label {
	font-size: 26rpx;
	color: #666;
}

.simple-value {
	font-size: 30rpx;
	color: #333;
	font-weight: 600;
}

.simple-value.earnings {
	color: #2196F3;
}

/* 详细模式 */
.time-period {
	border: 2rpx solid #f0f0f0;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
}

.time-period:last-child {
	margin-bottom: 0;
}

.period-header {
	padding: 20rpx 24rpx;
	background-color: #f8f9fa;
	border-bottom: 2rpx solid #f0f0f0;
}

.period-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #2196F3;
}

.period-grid {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 20rpx;
	padding: 24rpx;
}

.period-item {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
	text-align: center;
}

.period-label {
	font-size: 24rpx;
	color: #666;
}

.period-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.period-value.earnings {
	color: #2196F3;
	font-weight: 600;
}

/* 统计汇总网格 */
.summary-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30rpx;
	padding: 30rpx;
}

.summary-item {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	text-align: center;
	padding: 20rpx;
	background-color: #f8f9fa;
	border-radius: 12rpx;
}

.summary-label {
	font-size: 26rpx;
	color: #666;
}

.summary-value {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
}

.summary-value.earnings {
	color: #2196F3;
	font-size: 36rpx;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {

	.info-grid,
	.summary-grid {
		grid-template-columns: 1fr;
		gap: 20rpx;
	}

	.simple-grid,
	.period-grid {
		grid-template-columns: 1fr;
		gap: 15rpx;
	}

	.nav-title {
		font-size: 32rpx;
	}

	.edit-btn {
		font-size: 26rpx;
		padding: 10rpx 20rpx;
	}
}

/* 月报模式青色主题 */
.monthly-theme .nav-bar {
	background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
	border-bottom: 2rpx solid rgba(0, 188, 212, 0.3);
	box-shadow: 0 2rpx 8rpx rgba(0, 188, 212, 0.2);
}

.monthly-theme .nav-icon {
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.monthly-theme .nav-text {
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.monthly-theme .nav-title {
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.monthly-theme .edit-btn {
	background-color: rgba(255, 255, 255, 0.2);
	color: white;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.monthly-theme .edit-btn:active {
	background-color: rgba(255, 255, 255, 0.3);
	transform: scale(0.95);
}

.monthly-theme .save-btn {
	background-color: #f24251;
}

.monthly-theme .save-btn:not(.disabled):active {
	background-color: #e11919;
}

.monthly-theme .back-btn {
	background-color: #00BCD4;
}

.monthly-theme .card-header {
	background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%);
}

.monthly-theme .info-value.earnings,
.monthly-theme .simple-value.earnings,
.monthly-theme .period-value.earnings,
.monthly-theme .summary-value.earnings {
	color: #00BCD4;
}

.monthly-theme .period-title {
	color: #00BCD4;
}

.monthly-theme .edit-input:focus {
	border-color: #00BCD4;
}
</style>
