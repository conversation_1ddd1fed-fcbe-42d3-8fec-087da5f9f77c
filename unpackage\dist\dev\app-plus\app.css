*{margin:0;-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-tap-highlight-color:transparent}html,body{-webkit-user-select:none;user-select:none;width:100%}html{height:100%;height:100vh;width:100%;width:100vw}body{overflow-x:hidden;background-color:#fff;height:100%}#app{height:100%}input[type=search]::-webkit-search-cancel-button{display:none}.uni-loading,uni-button[loading]:before{background:transparent url(data:image/svg+xml;base64,\ PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat}.uni-loading{width:20px;height:20px;display:inline-block;vertical-align:middle;animation:uni-loading 1s steps(12,end) infinite;background-size:100%}@keyframes uni-loading{0%{transform:rotate3d(0,0,1,0)}to{transform:rotate3d(0,0,1,360deg)}}@media (prefers-color-scheme: dark){html{--UI-BG-COLOR-ACTIVE: #373737;--UI-BORDER-COLOR-1: #373737;--UI-BG: #000;--UI-BG-0: #191919;--UI-BG-1: #1f1f1f;--UI-BG-2: #232323;--UI-BG-3: #2f2f2f;--UI-BG-4: #606060;--UI-BG-5: #2c2c2c;--UI-FG: #fff;--UI-FG-0: hsla(0, 0%, 100%, .8);--UI-FG-HALF: hsla(0, 0%, 100%, .6);--UI-FG-1: hsla(0, 0%, 100%, .5);--UI-FG-2: hsla(0, 0%, 100%, .3);--UI-FG-3: hsla(0, 0%, 100%, .05)}body{background-color:var(--UI-BG-0);color:var(--UI-FG-0)}}[nvue] uni-view,[nvue] uni-label,[nvue] uni-swiper-item,[nvue] uni-scroll-view{display:flex;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}[nvue] uni-button{margin:0}[nvue-dir-row] uni-view,[nvue-dir-row] uni-label,[nvue-dir-row] uni-swiper-item{flex-direction:row}[nvue-dir-column] uni-view,[nvue-dir-column] uni-label,[nvue-dir-column] uni-swiper-item{flex-direction:column}[nvue-dir-row-reverse] uni-view,[nvue-dir-row-reverse] uni-label,[nvue-dir-row-reverse] uni-swiper-item{flex-direction:row-reverse}[nvue-dir-column-reverse] uni-view,[nvue-dir-column-reverse] uni-label,[nvue-dir-column-reverse] uni-swiper-item{flex-direction:column-reverse}[nvue] uni-view,[nvue] uni-image,[nvue] uni-input,[nvue] uni-scroll-view,[nvue] uni-swiper,[nvue] uni-swiper-item,[nvue] uni-text,[nvue] uni-textarea,[nvue] uni-video{position:relative;border:0px solid #000000;box-sizing:border-box}[nvue] uni-swiper-item{position:absolute}@keyframes once-show{0%{top:0}}uni-resize-sensor,uni-resize-sensor>div{position:absolute;left:0;top:0;right:0;bottom:0;overflow:hidden}uni-resize-sensor{display:block;z-index:-1;visibility:hidden;animation:once-show 1ms}uni-resize-sensor>div>div{position:absolute;left:0;top:0}uni-resize-sensor>div:first-child>div{width:100000px;height:100000px}uni-resize-sensor>div:last-child>div{width:200%;height:200%}uni-text[selectable]{cursor:auto;-webkit-user-select:text;user-select:text}uni-text{white-space:pre-line}uni-view{display:block}uni-view[hidden]{display:none}uni-button{position:relative;display:block;margin-left:auto;margin-right:auto;padding-left:14px;padding-right:14px;box-sizing:border-box;font-size:18px;text-align:center;text-decoration:none;line-height:2.55555556;border-radius:5px;-webkit-tap-highlight-color:transparent;overflow:hidden;color:#000;background-color:#f8f8f8;cursor:pointer}uni-button[hidden]{display:none!important}uni-button:after{content:" ";width:200%;height:200%;position:absolute;top:0;left:0;border:1px solid rgba(0,0,0,.2);transform:scale(.5);transform-origin:0 0;box-sizing:border-box;border-radius:10px}uni-button[native]{padding-left:0;padding-right:0}uni-button[native] .uni-button-cover-view-wrapper{border:inherit;border-color:inherit;border-radius:inherit;background-color:inherit}uni-button[native] .uni-button-cover-view-inner{padding-left:14px;padding-right:14px}uni-button uni-cover-view{line-height:inherit;white-space:inherit}uni-button[type=default]{color:#000;background-color:#f8f8f8}uni-button[type=primary]{color:#fff;background-color:#007aff}uni-button[type=warn]{color:#fff;background-color:#e64340}uni-button[disabled]{color:rgba(255,255,255,.6);cursor:not-allowed}uni-button[disabled][type=default],uni-button[disabled]:not([type]){color:rgba(0,0,0,.3);background-color:#f7f7f7}uni-button[disabled][type=primary]{background-color:rgba(0,122,255,.6)}uni-button[disabled][type=warn]{background-color:#ec8b89}uni-button[type=primary][plain]{color:#007aff;border:1px solid #007aff;background-color:transparent}uni-button[type=primary][plain][disabled]{color:rgba(0,0,0,.2);border-color:rgba(0,0,0,.2)}uni-button[type=primary][plain]:after{border-width:0}uni-button[type=default][plain]{color:#353535;border:1px solid #353535;background-color:transparent}uni-button[type=default][plain][disabled]{color:rgba(0,0,0,.2);border-color:rgba(0,0,0,.2)}uni-button[type=default][plain]:after{border-width:0}uni-button[plain]{color:#353535;border:1px solid #353535;background-color:transparent}uni-button[plain][disabled]{color:rgba(0,0,0,.2);border-color:rgba(0,0,0,.2)}uni-button[plain]:after{border-width:0}uni-button[plain][native] .uni-button-cover-view-inner{padding:0}uni-button[type=warn][plain]{color:#e64340;border:1px solid #e64340;background-color:transparent}uni-button[type=warn][plain][disabled]{color:rgba(0,0,0,.2);border-color:rgba(0,0,0,.2)}uni-button[type=warn][plain]:after{border-width:0}uni-button[size=mini]{display:inline-block;line-height:2.3;font-size:13px;padding:0 1.34em}uni-button[size=mini][native]{padding:0}uni-button[size=mini][native] .uni-button-cover-view-inner{padding:0 1.34em}uni-button[loading]:not([disabled]){cursor:progress}uni-button[loading]:before{content:" ";display:inline-block;width:18px;height:18px;vertical-align:middle;animation:uni-loading 1s steps(12,end) infinite;background-size:100%}uni-button[loading][type=primary]{color:rgba(255,255,255,.6);background-color:#0062cc}uni-button[loading][type=primary][plain]{color:#007aff;background-color:transparent}uni-button[loading][type=default]{color:rgba(0,0,0,.6);background-color:#dedede}uni-button[loading][type=default][plain]{color:#353535;background-color:transparent}uni-button[loading][type=warn]{color:rgba(255,255,255,.6);background-color:#ce3c39}uni-button[loading][type=warn][plain]{color:#e64340;background-color:transparent}uni-button[loading][native]:before{content:none}.button-hover{color:rgba(0,0,0,.6);background-color:#dedede}.button-hover[plain]{color:rgba(53,53,53,.6);border-color:rgba(53,53,53,.6);background-color:transparent}.button-hover[type=primary]{color:rgba(255,255,255,.6);background-color:#0062cc}.button-hover[type=primary][plain]{color:rgba(0,122,255,.6);border-color:rgba(0,122,255,.6);background-color:transparent}.button-hover[type=default]{color:rgba(0,0,0,.6);background-color:#dedede}.button-hover[type=default][plain]{color:rgba(53,53,53,.6);border-color:rgba(53,53,53,.6);background-color:transparent}.button-hover[type=warn]{color:rgba(255,255,255,.6);background-color:#ce3c39}.button-hover[type=warn][plain]{color:rgba(230,67,64,.6);border-color:rgba(230,67,64,.6);background-color:transparent}@media (prefers-color-scheme: dark){uni-button,uni-button[type=default]{color:#d6d6d6;background-color:#343434}.button-hover,.button-hover[type=default]{color:#d6d6d6;background-color:rgba(255,255,255,.1)}uni-button[disabled][type=default],uni-button[disabled]:not([type]){color:rgba(255,255,255,.2);background-color:rgba(255,255,255,.08)}uni-button[type=primary][plain][disabled]{color:rgba(255,255,255,.2);border-color:rgba(255,255,255,.2)}uni-button[type=default][plain]{color:#d6d6d6;border:1px solid #d6d6d6}.button-hover[type=default][plain]{color:rgba(150,150,150,.6);border-color:rgba(150,150,150,.6);background-color:rgba(50,50,50,.2)}uni-button[type=default][plain][disabled]{border-color:rgba(255,255,255,.2);color:rgba(255,255,255,.2)}}uni-canvas{width:300px;height:150px;display:block;position:relative}uni-canvas>.uni-canvas-canvas{position:absolute;top:0;left:0;width:100%;height:100%}uni-checkbox{-webkit-tap-highlight-color:transparent;display:inline-block;cursor:pointer}uni-checkbox[hidden]{display:none}uni-checkbox[disabled]{cursor:not-allowed}.uni-checkbox-wrapper{display:inline-flex;align-items:center;vertical-align:middle}.uni-checkbox-input{margin-right:5px;-webkit-appearance:none;appearance:none;outline:0;border:1px solid #d1d1d1;background-color:#fff;border-radius:3px;width:22px;height:22px;position:relative}.uni-checkbox-input svg{color:#007aff;font-size:22px;position:absolute;top:50%;left:50%;transform:translate(-50%,-48%) scale(.73)}@media (hover: hover){uni-checkbox:not([disabled]) .uni-checkbox-input:hover{border-color:var(--HOVER-BD-COLOR, #007aff)!important}}uni-checkbox-group{display:block}uni-checkbox-group[hidden]{display:none}uni-cover-image{display:block;line-height:1.2;overflow:hidden;height:100%;width:100%;pointer-events:auto}uni-cover-image[hidden]{display:none}uni-cover-image .uni-cover-image{width:100%;height:100%}uni-cover-view{display:block;line-height:1.2;overflow:hidden;white-space:nowrap;pointer-events:auto}uni-cover-view[hidden]{display:none}uni-cover-view .uni-cover-view{width:100%;height:100%;visibility:hidden;text-overflow:inherit;white-space:inherit;align-items:inherit;justify-content:inherit;flex-direction:inherit;flex-wrap:inherit;display:inherit;overflow:inherit}.ql-container{display:block;position:relative;box-sizing:border-box;-webkit-user-select:text;user-select:text;outline:none;overflow:hidden;width:100%;height:200px;min-height:200px}.ql-container[hidden]{display:none}.ql-container .ql-editor{position:relative;font-size:inherit;line-height:inherit;font-family:inherit;min-height:inherit;width:100%;height:100%;padding:0;overflow-x:hidden;overflow-y:auto;-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none;-webkit-overflow-scrolling:touch}.ql-container .ql-editor::-webkit-scrollbar{width:0!important}.ql-container .ql-editor.scroll-disabled{overflow:hidden}.ql-container .ql-image-overlay{display:flex;position:absolute;box-sizing:border-box;border:1px dashed #ccc;justify-content:center;align-items:center;-webkit-user-select:none;user-select:none}.ql-container .ql-image-overlay .ql-image-size{position:absolute;padding:4px 8px;text-align:center;background-color:#fff;color:#888;border:1px solid #ccc;box-sizing:border-box;opacity:.8;right:4px;top:4px;font-size:12px;display:inline-block;width:auto}.ql-container .ql-image-overlay .ql-image-toolbar{position:relative;text-align:center;box-sizing:border-box;background:#000;border-radius:5px;color:#fff;font-size:0;min-height:24px;z-index:100}.ql-container .ql-image-overlay .ql-image-toolbar span{display:inline-block;cursor:pointer;padding:5px;font-size:12px;border-right:1px solid #fff}.ql-container .ql-image-overlay .ql-image-toolbar span:last-child{border-right:0}.ql-container .ql-image-overlay .ql-image-toolbar span.triangle-up{padding:0;position:absolute;top:-12px;left:50%;transform:translate(-50%);width:0;height:0;border-width:6px;border-style:solid;border-color:transparent transparent black transparent}.ql-container .ql-image-overlay .ql-image-handle{position:absolute;height:12px;width:12px;border-radius:50%;border:1px solid #ccc;box-sizing:border-box;background:#fff}.ql-container img{display:inline-block;max-width:100%}.ql-clipboard p{margin:0;padding:0}.ql-editor{box-sizing:border-box;height:100%;outline:none;overflow-y:auto;tab-size:4;-moz-tab-size:4;text-align:left;white-space:pre-wrap;word-wrap:break-word}.ql-editor>*{cursor:text}.ql-editor p,.ql-editor ol,.ql-editor ul,.ql-editor pre,.ql-editor blockquote,.ql-editor h1,.ql-editor h2,.ql-editor h3,.ql-editor h4,.ql-editor h5,.ql-editor h6{margin:0;padding:0;counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol>li,.ql-editor ul>li{list-style-type:none}.ql-editor ul>li:before{content:"•"}.ql-editor ul[data-checked=true],.ql-editor ul[data-checked=false]{pointer-events:none}.ql-editor ul[data-checked=true]>li *,.ql-editor ul[data-checked=false]>li *{pointer-events:all}.ql-editor ul[data-checked=true]>li:before,.ql-editor ul[data-checked=false]>li:before{color:#777;cursor:pointer;pointer-events:all}.ql-editor ul[data-checked=true]>li:before{content:"☑"}.ql-editor ul[data-checked=false]>li:before{content:"☐"}.ql-editor li:before{display:inline-block;white-space:nowrap;width:2em}.ql-editor ol li{counter-reset:list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;counter-increment:list-0}.ql-editor ol li:before{content:counter(list-0,decimal) ". "}.ql-editor ol li.ql-indent-1{counter-increment:list-1}.ql-editor ol li.ql-indent-1:before{content:counter(list-1,lower-alpha) ". "}.ql-editor ol li.ql-indent-1{counter-reset:list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-2{counter-increment:list-2}.ql-editor ol li.ql-indent-2:before{content:counter(list-2,lower-roman) ". "}.ql-editor ol li.ql-indent-2{counter-reset:list-3 list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-3{counter-increment:list-3}.ql-editor ol li.ql-indent-3:before{content:counter(list-3,decimal) ". "}.ql-editor ol li.ql-indent-3{counter-reset:list-4 list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-4{counter-increment:list-4}.ql-editor ol li.ql-indent-4:before{content:counter(list-4,lower-alpha) ". "}.ql-editor ol li.ql-indent-4{counter-reset:list-5 list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-5{counter-increment:list-5}.ql-editor ol li.ql-indent-5:before{content:counter(list-5,lower-roman) ". "}.ql-editor ol li.ql-indent-5{counter-reset:list-6 list-7 list-8 list-9}.ql-editor ol li.ql-indent-6{counter-increment:list-6}.ql-editor ol li.ql-indent-6:before{content:counter(list-6,decimal) ". "}.ql-editor ol li.ql-indent-6{counter-reset:list-7 list-8 list-9}.ql-editor ol li.ql-indent-7{counter-increment:list-7}.ql-editor ol li.ql-indent-7:before{content:counter(list-7,lower-alpha) ". "}.ql-editor ol li.ql-indent-7{counter-reset:list-8 list-9}.ql-editor ol li.ql-indent-8{counter-increment:list-8}.ql-editor ol li.ql-indent-8:before{content:counter(list-8,lower-roman) ". "}.ql-editor ol li.ql-indent-8{counter-reset:list-9}.ql-editor ol li.ql-indent-9{counter-increment:list-9}.ql-editor ol li.ql-indent-9:before{content:counter(list-9,decimal) ". "}.ql-editor .ql-indent-1:not(.ql-direction-rtl){padding-left:2em}.ql-editor li.ql-indent-1:not(.ql-direction-rtl){padding-left:2em}.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right{padding-right:2em}.ql-editor .ql-indent-2:not(.ql-direction-rtl){padding-left:4em}.ql-editor li.ql-indent-2:not(.ql-direction-rtl){padding-left:4em}.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right{padding-right:4em}.ql-editor .ql-indent-3:not(.ql-direction-rtl){padding-left:6em}.ql-editor li.ql-indent-3:not(.ql-direction-rtl){padding-left:6em}.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right{padding-right:6em}.ql-editor .ql-indent-4:not(.ql-direction-rtl){padding-left:8em}.ql-editor li.ql-indent-4:not(.ql-direction-rtl){padding-left:8em}.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right{padding-right:8em}.ql-editor .ql-indent-5:not(.ql-direction-rtl){padding-left:10em}.ql-editor li.ql-indent-5:not(.ql-direction-rtl){padding-left:10em}.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right{padding-right:10em}.ql-editor .ql-indent-6:not(.ql-direction-rtl){padding-left:12em}.ql-editor li.ql-indent-6:not(.ql-direction-rtl){padding-left:12em}.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right{padding-right:12em}.ql-editor .ql-indent-7:not(.ql-direction-rtl){padding-left:14em}.ql-editor li.ql-indent-7:not(.ql-direction-rtl){padding-left:14em}.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right{padding-right:14em}.ql-editor .ql-indent-8:not(.ql-direction-rtl){padding-left:16em}.ql-editor li.ql-indent-8:not(.ql-direction-rtl){padding-left:16em}.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right{padding-right:16em}.ql-editor .ql-indent-9:not(.ql-direction-rtl){padding-left:18em}.ql-editor li.ql-indent-9:not(.ql-direction-rtl){padding-left:18em}.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right,.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right{padding-right:18em}.ql-editor .ql-direction-rtl{direction:rtl;text-align:inherit}.ql-editor .ql-align-center{text-align:center}.ql-editor .ql-align-justify{text-align:justify}.ql-editor .ql-align-right{text-align:right}.ql-editor.ql-blank:before{color:rgba(0,0,0,.6);content:attr(data-placeholder);font-style:italic;pointer-events:none;position:absolute}.ql-container.ql-disabled .ql-editor ul[data-checked]>li:before{pointer-events:none}.ql-clipboard{left:-100000px;height:1px;overflow-y:hidden;position:absolute;top:50%}uni-icon{display:inline-block;font-size:0;box-sizing:border-box}uni-icon[hidden]{display:none}uni-image{width:320px;height:240px;display:inline-block;overflow:hidden;position:relative}uni-image[hidden]{display:none}uni-image>div{width:100%;height:100%;background-repeat:no-repeat}uni-image>img{-webkit-touch-callout:none;-webkit-user-select:none;user-select:none;display:block;position:absolute;top:0;left:0;width:100%;height:100%;opacity:0}uni-image>.uni-image-will-change{will-change:transform}uni-input{display:block;font-size:16px;line-height:1.4em;height:1.4em;min-height:1.4em;overflow:hidden}uni-input[hidden]{display:none}.uni-input-wrapper,.uni-input-placeholder,.uni-input-form,.uni-input-input{outline:none;border:none;padding:0;margin:0;text-decoration:inherit}.uni-input-wrapper,.uni-input-form{display:flex;position:relative;width:100%;height:100%;flex-direction:column;justify-content:center}.uni-input-placeholder,.uni-input-input{width:100%}.uni-input-placeholder{position:absolute;top:auto!important;left:0;color:gray;overflow:hidden;text-overflow:clip;white-space:pre;word-break:keep-all;pointer-events:none;line-height:inherit}.uni-input-input{position:relative;display:block;height:100%;background:none;color:inherit;opacity:1;font:inherit;line-height:inherit;letter-spacing:inherit;text-align:inherit;text-indent:inherit;text-transform:inherit;text-shadow:inherit}.uni-input-input[type=search]::-webkit-search-cancel-button,.uni-input-input[type=search]::-webkit-search-decoration{display:none}.uni-input-input::-webkit-outer-spin-button,.uni-input-input::-webkit-inner-spin-button{-webkit-appearance:none;appearance:none;margin:0}.uni-input-input[type=number]{-moz-appearance:textfield}.uni-input-input:disabled{-webkit-text-fill-color:currentcolor}.uni-label-pointer{cursor:pointer}uni-live-pusher{width:320px;height:240px;display:inline-block;line-height:0;overflow:hidden;position:relative}uni-live-pusher[hidden]{display:none}.uni-live-pusher-container{width:100%;height:100%;position:absolute;top:0;left:0;overflow:hidden;background-color:#000}.uni-live-pusher-slot{position:absolute;top:0;width:100%;height:100%;overflow:hidden;pointer-events:none}uni-map{width:300px;height:225px;display:inline-block;line-height:0;overflow:hidden;position:relative}uni-map[hidden]{display:none}.uni-map-container{width:100%;height:100%;position:absolute;top:0;left:0;overflow:hidden;background-color:transparent}.uni-map-slot{position:absolute;top:0;width:100%;height:100%;overflow:hidden;pointer-events:none}uni-map.web{position:relative;width:300px;height:150px;display:block}uni-map.web[hidden]{display:none}uni-map.web .amap-marker-label{padding:0;border:none;background-color:transparent}uni-map.web .amap-marker>.amap-icon>img{left:0!important;top:0!important}uni-map.web .uni-map-control{position:absolute;width:0;height:0;top:0;left:0;z-index:999}uni-map.web .uni-map-control-icon{position:absolute;max-width:initial}.uni-system-choose-location{display:block;position:fixed;left:0;top:0;width:100%;height:100%;background:#f8f8f8;z-index:999}.uni-system-choose-location .map{position:absolute;top:0;left:0;width:100%;height:300px}.uni-system-choose-location .map-location{position:absolute;left:50%;bottom:50%;width:32px;height:52px;margin-left:-16px;cursor:pointer;background-size:100%}.uni-system-choose-location .map-move{position:absolute;bottom:50px;right:10px;width:40px;height:40px;box-sizing:border-box;line-height:40px;background-color:#fff;border-radius:50%;pointer-events:auto;cursor:pointer;box-shadow:0 0 5px 1px rgba(0,0,0,.3)}.uni-system-choose-location .map-move>svg{display:block;width:100%;height:100%;box-sizing:border-box;padding:8px}.uni-system-choose-location .nav{position:absolute;top:0;left:0;width:100%;height:calc(44px + var(--status-bar-height));background-color:transparent;background-image:linear-gradient(to bottom,rgba(0,0,0,.3),rgba(0,0,0,0))}.uni-system-choose-location .nav-btn{position:absolute;box-sizing:border-box;top:var(--status-bar-height);left:0;width:60px;height:44px;padding:6px;line-height:32px;font-size:26px;color:#fff;text-align:center;cursor:pointer}.uni-system-choose-location .nav-btn.confirm{left:auto;right:0}.uni-system-choose-location .nav-btn.disable{opacity:.4}.uni-system-choose-location .nav-btn>svg{display:block;width:100%;height:100%;border-radius:2px;box-sizing:border-box;padding:3px}.uni-system-choose-location .nav-btn.confirm>svg{background-color:#007aff;padding:5px}.uni-system-choose-location .menu{position:absolute;top:300px;left:0;width:100%;bottom:0;background-color:#fff}.uni-system-choose-location .search{display:flex;flex-direction:row;height:50px;padding:8px;line-height:34px;box-sizing:border-box;background-color:#fff}.uni-system-choose-location .search-input{flex:1;height:100%;border-radius:5px;padding:0 5px;background:#ebebeb}.uni-system-choose-location .search-btn{margin-left:5px;color:#007aff;font-size:17px;text-align:center}.uni-system-choose-location .list{position:absolute;top:50px;left:0;width:100%;bottom:0;padding-bottom:10px}.uni-system-choose-location .list-loading{display:flex;height:50px;justify-content:center;align-items:center}.uni-system-choose-location .list-item{position:relative;padding:10px 40px 10px 10px;cursor:pointer}.uni-system-choose-location .list-item>svg{display:none;position:absolute;top:50%;right:10px;width:30px;height:30px;margin-top:-15px;box-sizing:border-box;padding:5px}.uni-system-choose-location .list-item.selected>svg{display:block}.uni-system-choose-location .list-item:not(:last-child):after{position:absolute;content:"";height:1px;left:10px;bottom:0;width:100%;background-color:#d3d3d3}.uni-system-choose-location .list-item-title{font-size:14px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.uni-system-choose-location .list-item-detail{font-size:12px;color:gray;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}@media screen and (min-width: 800px){.uni-system-choose-location .map{top:0;height:100%}.uni-system-choose-location .map-move{bottom:10px;right:320px}.uni-system-choose-location .menu{top:calc(54px + var(--status-bar-height));left:auto;right:10px;width:300px;bottom:10px;max-height:600px;box-shadow:0 0 20px 5px rgba(0,0,0,.3)}}.uni-system-open-location{display:block;position:fixed;left:0;top:0;width:100%;height:100%;background:#f8f8f8;z-index:999}.uni-system-open-location .map{position:absolute;top:0;left:0;width:100%;bottom:80px;height:auto}.uni-system-open-location .info{position:absolute;bottom:0;left:0;width:100%;height:80px;background-color:#fff;padding:15px;box-sizing:border-box;line-height:1.5}.uni-system-open-location .info>.name{font-size:17px;color:#111}.uni-system-open-location .info>.address{font-size:14px;color:#666}.uni-system-open-location .info>.nav{position:absolute;top:50%;right:15px;width:50px;height:50px;border-radius:50%;margin-top:-25px;background-color:#007aff}.uni-system-open-location .info>.nav>svg{display:block;width:100%;height:100%;padding:10px;box-sizing:border-box}.uni-system-open-location .map-move{position:absolute;bottom:50px;right:10px;width:40px;height:40px;box-sizing:border-box;line-height:40px;background-color:#fff;border-radius:50%;pointer-events:auto;cursor:pointer;box-shadow:0 0 5px 1px rgba(0,0,0,.3)}.uni-system-open-location .map-move>svg{display:block;width:100%;height:100%;box-sizing:border-box;padding:8px}.uni-system-open-location .nav-btn-back{position:absolute;box-sizing:border-box;top:var(--status-bar-height);left:0;width:44px;height:44px;padding:6px;cursor:pointer}.uni-system-open-location .nav-btn-back>svg{display:block;width:100%;height:100%;border-radius:50%;background-color:rgba(0,0,0,.5);padding:3px;box-sizing:border-box}.uni-system-open-location .map-content{position:absolute;left:0;top:0;width:100%;bottom:0;overflow:hidden}.uni-system-open-location .map-content.fix-position{top:-74px;bottom:-44px}.uni-system-open-location .map-content>iframe{width:100%;height:100%;border:none}.uni-system-open-location .actTonav{position:absolute;right:16px;bottom:56px;width:60px;height:60px;border-radius:60px}.uni-system-open-location .nav-view{position:absolute;left:0;top:0;width:100%;height:100%;display:flex;flex-direction:column}.uni-system-open-location .nav-view-top-placeholder{width:100%;height:var(--status-bar-height);background-color:#fff}.uni-system-open-location .nav-view-frame{width:100%;flex:1}uni-movable-area{display:block;position:relative;width:10px;height:10px}uni-movable-area[hidden]{display:none}uni-movable-view{display:inline-block;width:10px;height:10px;top:0;left:0;position:absolute;cursor:grab}uni-movable-view[hidden]{display:none}uni-navigator{height:auto;width:auto;display:block;cursor:pointer}uni-navigator[hidden]{display:none}.navigator-hover{background-color:rgba(0,0,0,.1);opacity:.7}.navigator-wrap,.navigator-wrap:link,.navigator-wrap:visited,.navigator-wrap:hover,.navigator-wrap:active{text-decoration:none;color:inherit;cursor:pointer}uni-picker-view{display:block}.uni-picker-view-wrapper{display:flex;position:relative;overflow:hidden;height:100%}uni-picker-view[hidden]{display:none}uni-picker-view-column{flex:1;position:relative;height:100%;overflow:hidden}uni-picker-view-column[hidden]{display:none}.uni-picker-view-group{height:100%;overflow:hidden}.uni-picker-view-mask{transform:translateZ(0)}.uni-picker-view-indicator,.uni-picker-view-mask{position:absolute;left:0;width:100%;z-index:3;pointer-events:none}.uni-picker-view-mask{top:0;height:100%;margin:0 auto;background-image:linear-gradient(180deg,rgba(255,255,255,.95),rgba(255,255,255,.6)),linear-gradient(0deg,rgba(255,255,255,.95),rgba(255,255,255,.6));background-position:top,bottom;background-size:100% 102px;background-repeat:no-repeat;transform:translateZ(0)}.uni-picker-view-indicator{height:34px;top:50%;transform:translateY(-50%)}.uni-picker-view-content{position:absolute;top:0;left:0;width:100%;will-change:transform;padding:102px 0;cursor:pointer}.uni-picker-view-content>*{height:var(--picker-view-column-indicator-height);overflow:hidden}.uni-picker-view-indicator:before{top:0;border-top:1px solid #e5e5e5;transform-origin:0 0;transform:scaleY(.5)}.uni-picker-view-indicator:after{bottom:0;border-bottom:1px solid #e5e5e5;transform-origin:0 100%;transform:scaleY(.5)}.uni-picker-view-indicator:after,.uni-picker-view-indicator:before{content:" ";position:absolute;left:0;right:0;height:1px;color:#e5e5e5}@media (prefers-color-scheme: dark){.uni-picker-view-indicator:before{border-top-color:var(--UI-FG-3)}.uni-picker-view-indicator:after{border-bottom-color:var(--UI-FG-3)}.uni-picker-view-mask{background-image:linear-gradient(180deg,rgba(35,35,35,.95),rgba(35,35,35,.6)),linear-gradient(0deg,rgba(35,35,35,.95),rgba(35,35,35,.6))}}uni-progress{display:flex;align-items:center}uni-progress[hidden]{display:none}.uni-progress-bar{flex:1}.uni-progress-inner-bar{width:0;height:100%}.uni-progress-info{margin-top:0;margin-bottom:0;min-width:2em;margin-left:15px;font-size:16px}uni-radio{-webkit-tap-highlight-color:transparent;display:inline-block;cursor:pointer}uni-radio[hidden]{display:none}uni-radio[disabled]{cursor:not-allowed}.uni-radio-wrapper{display:inline-flex;align-items:center;vertical-align:middle}.uni-radio-input{-webkit-appearance:none;appearance:none;margin-right:5px;outline:0;border:1px solid #d1d1d1;background-color:#fff;border-radius:50%;width:22px;height:22px;position:relative}@media (hover: hover){uni-radio:not([disabled]) .uni-radio-input:hover{border-color:var(--HOVER-BD-COLOR, #007aff)!important}}.uni-radio-input svg{color:#fff;font-size:18px;position:absolute;top:50%;left:50%;transform:translate(-50%,-48%) scale(.73)}.uni-radio-input.uni-radio-input-disabled{background-color:#e1e1e1;border-color:#d1d1d1}.uni-radio-input.uni-radio-input-disabled svg{color:#adadad}uni-radio-group{display:block}uni-radio-group[hidden]{display:none}uni-scroll-view{display:block;width:100%}uni-scroll-view[hidden]{display:none}.uni-scroll-view{position:relative;-webkit-overflow-scrolling:touch;width:100%;height:100%;max-height:inherit}.uni-scroll-view-scrollbar-hidden::-webkit-scrollbar{display:none}.uni-scroll-view-scrollbar-hidden{-moz-scrollbars:none;scrollbar-width:none}.uni-scroll-view-content{width:100%;height:100%}.uni-scroll-view-refresher{position:relative;overflow:hidden;flex-shrink:0}.uni-scroll-view-refresher-container{position:absolute;width:100%;bottom:0;display:flex;flex-direction:column-reverse}.uni-scroll-view-refresh{position:absolute;top:0;left:0;right:0;bottom:0;display:flex;flex-direction:row;justify-content:center;align-items:center}.uni-scroll-view-refresh-inner{display:flex;align-items:center;justify-content:center;line-height:0;width:40px;height:40px;border-radius:50%;background-color:#fff;box-shadow:0 1px 6px rgba(0,0,0,.118),0 1px 4px rgba(0,0,0,.118)}.uni-scroll-view-refresh__spinner{transform-origin:center center;animation:uni-scroll-view-refresh-rotate 2s linear infinite}.uni-scroll-view-refresh__spinner>circle{stroke:currentColor;stroke-linecap:round;animation:uni-scroll-view-refresh-dash 2s linear infinite}@keyframes uni-scroll-view-refresh-rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes uni-scroll-view-refresh-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}uni-slider{margin:10px 18px;padding:0;display:block}uni-slider[hidden]{display:none}uni-slider .uni-slider-wrapper{display:flex;align-items:center;min-height:16px}uni-slider .uni-slider-tap-area{flex:1;padding:8px 0}uni-slider .uni-slider-handle-wrapper{position:relative;height:2px;border-radius:5px;background-color:#e9e9e9;cursor:pointer;transition:background-color .3s ease;-webkit-tap-highlight-color:transparent}uni-slider .uni-slider-track{height:100%;border-radius:6px;background-color:#007aff;transition:background-color .3s ease}uni-slider .uni-slider-handle,uni-slider .uni-slider-thumb{position:absolute;left:50%;top:50%;cursor:pointer;border-radius:50%;transition:border-color .3s ease}uni-slider .uni-slider-handle{width:28px;height:28px;margin-top:-14px;margin-left:-14px;background-color:transparent;z-index:3;cursor:grab}uni-slider .uni-slider-thumb{z-index:2;box-shadow:0 0 4px rgba(0,0,0,.2)}uni-slider .uni-slider-step{position:absolute;width:100%;height:2px;background:transparent;z-index:1}uni-slider .uni-slider-value{width:3ch;color:#888;font-size:14px;margin-left:1em}uni-slider .uni-slider-disabled .uni-slider-track{background-color:#ccc}uni-slider .uni-slider-disabled .uni-slider-thumb{background-color:#fff;border-color:#ccc}uni-swiper{display:block;height:150px}uni-swiper[hidden]{display:none}.uni-swiper-wrapper{overflow:hidden;position:relative;width:100%;height:100%;transform:translateZ(0)}.uni-swiper-slides{position:absolute;left:0;top:0;right:0;bottom:0}.uni-swiper-slide-frame{position:absolute;left:0;top:0;width:100%;height:100%;will-change:transform}.uni-swiper-dots{position:absolute;font-size:0}.uni-swiper-dots-horizontal{left:50%;bottom:10px;text-align:center;white-space:nowrap;transform:translate(-50%)}.uni-swiper-dots-horizontal .uni-swiper-dot{margin-right:8px}.uni-swiper-dots-horizontal .uni-swiper-dot:last-child{margin-right:0}.uni-swiper-dots-vertical{right:10px;top:50%;text-align:right;transform:translateY(-50%)}.uni-swiper-dots-vertical .uni-swiper-dot{display:block;margin-bottom:9px}.uni-swiper-dots-vertical .uni-swiper-dot:last-child{margin-bottom:0}.uni-swiper-dot{display:inline-block;width:8px;height:8px;cursor:pointer;transition-property:background-color;transition-timing-function:ease;background:rgba(0,0,0,.3);border-radius:50%}.uni-swiper-dot-active{background-color:#000}.uni-swiper-navigation{width:26px;height:26px;cursor:pointer;position:absolute;top:50%;margin-top:-13px;display:flex;align-items:center;transition:all .2s;border-radius:50%;opacity:1}.uni-swiper-navigation-disabled{opacity:.35;cursor:not-allowed}.uni-swiper-navigation-hide{opacity:0;cursor:auto;pointer-events:none}.uni-swiper-navigation-prev{left:10px}.uni-swiper-navigation-prev svg{margin-left:-1px;left:10px}.uni-swiper-navigation-prev.uni-swiper-navigation-vertical{top:18px;left:50%;margin-left:-13px}.uni-swiper-navigation-prev.uni-swiper-navigation-vertical svg{transform:rotate(90deg);margin-left:auto;margin-top:-2px}.uni-swiper-navigation-next{right:10px}.uni-swiper-navigation-next svg{transform:rotate(180deg)}.uni-swiper-navigation-next.uni-swiper-navigation-vertical{top:auto;bottom:5px;left:50%;margin-left:-13px}.uni-swiper-navigation-next.uni-swiper-navigation-vertical svg{margin-top:2px;transform:rotate(270deg)}uni-swiper-item{display:block;overflow:hidden;will-change:transform;position:absolute;width:100%;height:100%;cursor:grab}uni-swiper-item[hidden]{display:none}uni-switch{-webkit-tap-highlight-color:transparent;display:inline-block;cursor:pointer}uni-switch[hidden]{display:none}uni-switch[disabled]{cursor:not-allowed}uni-switch[disabled] .uni-switch-input{opacity:.7}.uni-switch-wrapper{display:inline-flex;align-items:center;vertical-align:middle}.uni-switch-input{-webkit-appearance:none;appearance:none;position:relative;width:52px;height:32px;margin-right:5px;border:1px solid #dfdfdf;outline:0;border-radius:16px;box-sizing:border-box;background-color:#dfdfdf;transition:background-color .1s,border .1s}.uni-switch-input:before{content:" ";position:absolute;top:0;left:0;width:50px;height:30px;border-radius:15px;background-color:#fdfdfd;transition:transform .3s}.uni-switch-input:after{content:" ";position:absolute;top:0;left:0;width:30px;height:30px;border-radius:15px;background-color:#fff;box-shadow:0 1px 3px rgba(0,0,0,.4);transition:transform .3s}.uni-switch-input.uni-switch-input-checked{border-color:#007aff;background-color:#007aff}.uni-switch-input.uni-switch-input-checked:before{transform:scale(0)}.uni-switch-input.uni-switch-input-checked:after{transform:translate(20px)}uni-switch .uni-checkbox-input{margin-right:5px;-webkit-appearance:none;appearance:none;outline:0;border:1px solid #d1d1d1;background-color:#fff;border-radius:3px;width:22px;height:22px;position:relative;color:#007aff}uni-switch:not([disabled]) .uni-checkbox-input:hover{border-color:#007aff}uni-switch .uni-checkbox-input svg{fill:#007aff;font-size:22px;position:absolute;top:50%;left:50%;transform:translate(-50%,-48%) scale(.73)}.uni-checkbox-input.uni-checkbox-input-disabled{background-color:#e1e1e1}.uni-checkbox-input.uni-checkbox-input-disabled:before{color:#adadad}@media (prefers-color-scheme: dark){uni-switch .uni-switch-input{border-color:#3b3b3f}uni-switch .uni-switch-input,uni-switch .uni-switch-input:before{background-color:#3b3b3f}uni-switch .uni-switch-input:after{background-color:#fff;box-shadow:0 1px 3px rgba(0,0,0,.4)}uni-switch .uni-checkbox-input{background-color:#2c2c2c;border:1px solid #656565}}uni-textarea{width:300px;height:150px;display:block;position:relative;font-size:16px;line-height:normal;white-space:pre-wrap;word-break:break-all}uni-textarea[hidden]{display:none}.uni-textarea-wrapper,.uni-textarea-placeholder,.uni-textarea-line,.uni-textarea-compute,.uni-textarea-textarea{outline:none;border:none;padding:0;margin:0;text-decoration:inherit}.uni-textarea-wrapper{display:block;position:relative;width:100%;height:100%;min-height:inherit;overflow-y:hidden}.uni-textarea-placeholder,.uni-textarea-line,.uni-textarea-compute,.uni-textarea-textarea{position:absolute;width:100%;height:100%;left:0;top:0;white-space:inherit;word-break:inherit}.uni-textarea-placeholder{color:gray;overflow:hidden}.uni-textarea-line,.uni-textarea-compute{visibility:hidden;height:auto}.uni-textarea-line{width:1em}.uni-textarea-textarea{resize:none;background:none;color:inherit;opacity:1;font:inherit;line-height:inherit;letter-spacing:inherit;text-align:inherit;text-indent:inherit;text-transform:inherit;text-shadow:inherit}.uni-textarea-textarea-fix-margin{width:auto;right:0;margin:0 -3px}.uni-textarea-textarea:disabled{-webkit-text-fill-color:currentcolor}uni-video{width:300px;height:225px;display:inline-block;line-height:0;overflow:hidden;position:relative}uni-video[hidden]{display:none}.uni-video-container{width:100%;height:100%;position:absolute;top:0;left:0;overflow:hidden;background-color:#000}.uni-video-slot{position:absolute;top:0;width:100%;height:100%;overflow:hidden;pointer-events:none}uni-web-view{display:inline-block;position:absolute;left:0;right:0;top:0;bottom:0}

/* 水平间距 */
/* 水平间距 */
/*每个页面公共css */
/* 水平间距 */
@font-face {
  font-family: "customicons"; /* Project id 2878519 */
  src:url('static/customicons.ttf') format('truetype');
}
.customicons {
  font-family: "customicons" !important;
}
.youxi:before {
  content: "\e60e";
}
.wenjian:before {
  content: "\e60f";
}
.zhuanfa:before {
  content: "\e610";
}
.uni-border {
  border: 1px #F0F0F0 solid;
}
.uni-primary {
  color: #2979ff;
}
.uni-primary-bg {
  background-color: #2979ff;
}
.uni-primary-disable {
  color: #94bcff;
}
.uni-primary-disable-bg {
  background-color: #94bcff;
}
.uni-primary-light {
  color: #d4e4ff;
}
.uni-primary-light-bg {
  background-color: #d4e4ff;
}
.uni-success {
  color: #18bc37;
}
.uni-success-bg {
  background-color: #18bc37;
}
.uni-success-disable {
  color: #8cde9b;
}
.uni-success-disable-bg {
  background-color: #8cde9b;
}
.uni-success-light {
  color: #d1f2d7;
}
.uni-success-light-bg {
  background-color: #d1f2d7;
}
.uni-warning {
  color: #f3a73f;
}
.uni-warning-bg {
  background-color: #f3a73f;
}
.uni-warning-disable {
  color: #f9d39f;
}
.uni-warning-disable-bg {
  background-color: #f9d39f;
}
.uni-warning-light {
  color: #fdedd9;
}
.uni-warning-light-bg {
  background-color: #fdedd9;
}
.uni-error {
  color: #e43d33;
}
.uni-error-bg {
  background-color: #e43d33;
}
.uni-error-disable {
  color: #f29e99;
}
.uni-error-disable-bg {
  background-color: #f29e99;
}
.uni-error-light {
  color: #fad8d6;
}
.uni-error-light-bg {
  background-color: #fad8d6;
}
.uni-info {
  color: #8f939c;
}
.uni-info-bg {
  background-color: #8f939c;
}
.uni-info-disable {
  color: #c7c9ce;
}
.uni-info-disable-bg {
  background-color: #c7c9ce;
}
.uni-info-light {
  color: #e9e9eb;
}
.uni-info-light-bg {
  background-color: #e9e9eb;
}
.uni-main-color {
  color: #3a3a3a;
}
.uni-main-color-bg {
  background-color: #3a3a3a;
}
.uni-base-color {
  color: #6a6a6a;
}
.uni-base-color-bg {
  background-color: #6a6a6a;
}
.uni-secondary-color {
  color: #909399;
}
.uni-secondary-color-bg {
  background-color: #909399;
}
.uni-extra-color {
  color: #c7c7c7;
}
.uni-extra-color-bg {
  background-color: #c7c7c7;
}
.uni-bg-color {
  color: #f7f7f7;
}
.uni-bg-color-bg {
  background-color: #f7f7f7;
}
.uni-border-1 {
  color: #F0F0F0;
}
.uni-border-1-bg {
  background-color: #F0F0F0;
}
.uni-border-2 {
  color: #EDEDED;
}
.uni-border-2-bg {
  background-color: #EDEDED;
}
.uni-border-3 {
  color: #DCDCDC;
}
.uni-border-3-bg {
  background-color: #DCDCDC;
}
.uni-border-4 {
  color: #B9B9B9;
}
.uni-border-4-bg {
  background-color: #B9B9B9;
}
.uni-black {
  color: #000000;
}
.uni-black-bg {
  background-color: #000000;
}
.uni-white {
  color: #ffffff;
}
.uni-white-bg {
  background-color: #ffffff;
}
.uni-transparent {
  color: rgba(0, 0, 0, 0);
}
.uni-transparent-bg {
  background-color: rgba(0, 0, 0, 0);
}
.uni-shadow-sm {
  box-shadow: 0 0 5px rgba(216, 216, 216, 0.5);
}
.uni-shadow-base {
  box-shadow: 0 1px 8px 1px rgba(165, 165, 165, 0.2);
}
.uni-shadow-lg {
  box-shadow: 0px 1px 10px 2px rgba(165, 164, 164, 0.5);
}
.uni-mask {
  background-color: rgba(0, 0, 0, 0.4);
}
.uni-mt-0 {
  margin-top: 0px;
}
.uni-mt-n0 {
  margin-top: 0px;
}
.uni-mr-0 {
  margin-right: 0px;
}
.uni-mr-n0 {
  margin-right: 0px;
}
.uni-mb-0 {
  margin-bottom: 0px;
}
.uni-mb-n0 {
  margin-bottom: 0px;
}
.uni-ml-0 {
  margin-left: 0px;
}
.uni-ml-n0 {
  margin-left: 0px;
}
.uni-mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}
.uni-mx-n0 {
  margin-left: 0px;
  margin-right: 0px;
}
.uni-my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.uni-my-n0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.uni-ma-0 {
  margin: 0px;
}
.uni-ma-n0 {
  margin: 0px;
}
.uni-mt-1 {
  margin-top: 2px;
}
.uni-mt-n1 {
  margin-top: -2px;
}
.uni-mr-1 {
  margin-right: 2px;
}
.uni-mr-n1 {
  margin-right: -2px;
}
.uni-mb-1 {
  margin-bottom: 2px;
}
.uni-mb-n1 {
  margin-bottom: -2px;
}
.uni-ml-1 {
  margin-left: 2px;
}
.uni-ml-n1 {
  margin-left: -2px;
}
.uni-mx-1 {
  margin-left: 2px;
  margin-right: 2px;
}
.uni-mx-n1 {
  margin-left: -2px;
  margin-right: -2px;
}
.uni-my-1 {
  margin-top: 2px;
  margin-bottom: 2px;
}
.uni-my-n1 {
  margin-top: -2px;
  margin-bottom: -2px;
}
.uni-ma-1 {
  margin: 2px;
}
.uni-ma-n1 {
  margin: -2px;
}
.uni-mt-2 {
  margin-top: 4px;
}
.uni-mt-n2 {
  margin-top: -4px;
}
.uni-mr-2 {
  margin-right: 4px;
}
.uni-mr-n2 {
  margin-right: -4px;
}
.uni-mb-2 {
  margin-bottom: 4px;
}
.uni-mb-n2 {
  margin-bottom: -4px;
}
.uni-ml-2 {
  margin-left: 4px;
}
.uni-ml-n2 {
  margin-left: -4px;
}
.uni-mx-2 {
  margin-left: 4px;
  margin-right: 4px;
}
.uni-mx-n2 {
  margin-left: -4px;
  margin-right: -4px;
}
.uni-my-2 {
  margin-top: 4px;
  margin-bottom: 4px;
}
.uni-my-n2 {
  margin-top: -4px;
  margin-bottom: -4px;
}
.uni-ma-2 {
  margin: 4px;
}
.uni-ma-n2 {
  margin: -4px;
}
.uni-mt-3 {
  margin-top: 6px;
}
.uni-mt-n3 {
  margin-top: -6px;
}
.uni-mr-3 {
  margin-right: 6px;
}
.uni-mr-n3 {
  margin-right: -6px;
}
.uni-mb-3 {
  margin-bottom: 6px;
}
.uni-mb-n3 {
  margin-bottom: -6px;
}
.uni-ml-3 {
  margin-left: 6px;
}
.uni-ml-n3 {
  margin-left: -6px;
}
.uni-mx-3 {
  margin-left: 6px;
  margin-right: 6px;
}
.uni-mx-n3 {
  margin-left: -6px;
  margin-right: -6px;
}
.uni-my-3 {
  margin-top: 6px;
  margin-bottom: 6px;
}
.uni-my-n3 {
  margin-top: -6px;
  margin-bottom: -6px;
}
.uni-ma-3 {
  margin: 6px;
}
.uni-ma-n3 {
  margin: -6px;
}
.uni-mt-4 {
  margin-top: 8px;
}
.uni-mt-n4 {
  margin-top: -8px;
}
.uni-mr-4 {
  margin-right: 8px;
}
.uni-mr-n4 {
  margin-right: -8px;
}
.uni-mb-4 {
  margin-bottom: 8px;
}
.uni-mb-n4 {
  margin-bottom: -8px;
}
.uni-ml-4 {
  margin-left: 8px;
}
.uni-ml-n4 {
  margin-left: -8px;
}
.uni-mx-4 {
  margin-left: 8px;
  margin-right: 8px;
}
.uni-mx-n4 {
  margin-left: -8px;
  margin-right: -8px;
}
.uni-my-4 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.uni-my-n4 {
  margin-top: -8px;
  margin-bottom: -8px;
}
.uni-ma-4 {
  margin: 8px;
}
.uni-ma-n4 {
  margin: -8px;
}
.uni-mt-5 {
  margin-top: 10px;
}
.uni-mt-n5 {
  margin-top: -10px;
}
.uni-mr-5 {
  margin-right: 10px;
}
.uni-mr-n5 {
  margin-right: -10px;
}
.uni-mb-5 {
  margin-bottom: 10px;
}
.uni-mb-n5 {
  margin-bottom: -10px;
}
.uni-ml-5 {
  margin-left: 10px;
}
.uni-ml-n5 {
  margin-left: -10px;
}
.uni-mx-5 {
  margin-left: 10px;
  margin-right: 10px;
}
.uni-mx-n5 {
  margin-left: -10px;
  margin-right: -10px;
}
.uni-my-5 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.uni-my-n5 {
  margin-top: -10px;
  margin-bottom: -10px;
}
.uni-ma-5 {
  margin: 10px;
}
.uni-ma-n5 {
  margin: -10px;
}
.uni-mt-6 {
  margin-top: 12px;
}
.uni-mt-n6 {
  margin-top: -12px;
}
.uni-mr-6 {
  margin-right: 12px;
}
.uni-mr-n6 {
  margin-right: -12px;
}
.uni-mb-6 {
  margin-bottom: 12px;
}
.uni-mb-n6 {
  margin-bottom: -12px;
}
.uni-ml-6 {
  margin-left: 12px;
}
.uni-ml-n6 {
  margin-left: -12px;
}
.uni-mx-6 {
  margin-left: 12px;
  margin-right: 12px;
}
.uni-mx-n6 {
  margin-left: -12px;
  margin-right: -12px;
}
.uni-my-6 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.uni-my-n6 {
  margin-top: -12px;
  margin-bottom: -12px;
}
.uni-ma-6 {
  margin: 12px;
}
.uni-ma-n6 {
  margin: -12px;
}
.uni-mt-7 {
  margin-top: 14px;
}
.uni-mt-n7 {
  margin-top: -14px;
}
.uni-mr-7 {
  margin-right: 14px;
}
.uni-mr-n7 {
  margin-right: -14px;
}
.uni-mb-7 {
  margin-bottom: 14px;
}
.uni-mb-n7 {
  margin-bottom: -14px;
}
.uni-ml-7 {
  margin-left: 14px;
}
.uni-ml-n7 {
  margin-left: -14px;
}
.uni-mx-7 {
  margin-left: 14px;
  margin-right: 14px;
}
.uni-mx-n7 {
  margin-left: -14px;
  margin-right: -14px;
}
.uni-my-7 {
  margin-top: 14px;
  margin-bottom: 14px;
}
.uni-my-n7 {
  margin-top: -14px;
  margin-bottom: -14px;
}
.uni-ma-7 {
  margin: 14px;
}
.uni-ma-n7 {
  margin: -14px;
}
.uni-mt-8 {
  margin-top: 16px;
}
.uni-mt-n8 {
  margin-top: -16px;
}
.uni-mr-8 {
  margin-right: 16px;
}
.uni-mr-n8 {
  margin-right: -16px;
}
.uni-mb-8 {
  margin-bottom: 16px;
}
.uni-mb-n8 {
  margin-bottom: -16px;
}
.uni-ml-8 {
  margin-left: 16px;
}
.uni-ml-n8 {
  margin-left: -16px;
}
.uni-mx-8 {
  margin-left: 16px;
  margin-right: 16px;
}
.uni-mx-n8 {
  margin-left: -16px;
  margin-right: -16px;
}
.uni-my-8 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.uni-my-n8 {
  margin-top: -16px;
  margin-bottom: -16px;
}
.uni-ma-8 {
  margin: 16px;
}
.uni-ma-n8 {
  margin: -16px;
}
.uni-mt-9 {
  margin-top: 18px;
}
.uni-mt-n9 {
  margin-top: -18px;
}
.uni-mr-9 {
  margin-right: 18px;
}
.uni-mr-n9 {
  margin-right: -18px;
}
.uni-mb-9 {
  margin-bottom: 18px;
}
.uni-mb-n9 {
  margin-bottom: -18px;
}
.uni-ml-9 {
  margin-left: 18px;
}
.uni-ml-n9 {
  margin-left: -18px;
}
.uni-mx-9 {
  margin-left: 18px;
  margin-right: 18px;
}
.uni-mx-n9 {
  margin-left: -18px;
  margin-right: -18px;
}
.uni-my-9 {
  margin-top: 18px;
  margin-bottom: 18px;
}
.uni-my-n9 {
  margin-top: -18px;
  margin-bottom: -18px;
}
.uni-ma-9 {
  margin: 18px;
}
.uni-ma-n9 {
  margin: -18px;
}
.uni-mt-10 {
  margin-top: 20px;
}
.uni-mt-n10 {
  margin-top: -20px;
}
.uni-mr-10 {
  margin-right: 20px;
}
.uni-mr-n10 {
  margin-right: -20px;
}
.uni-mb-10 {
  margin-bottom: 20px;
}
.uni-mb-n10 {
  margin-bottom: -20px;
}
.uni-ml-10 {
  margin-left: 20px;
}
.uni-ml-n10 {
  margin-left: -20px;
}
.uni-mx-10 {
  margin-left: 20px;
  margin-right: 20px;
}
.uni-mx-n10 {
  margin-left: -20px;
  margin-right: -20px;
}
.uni-my-10 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.uni-my-n10 {
  margin-top: -20px;
  margin-bottom: -20px;
}
.uni-ma-10 {
  margin: 20px;
}
.uni-ma-n10 {
  margin: -20px;
}
.uni-mt-11 {
  margin-top: 22px;
}
.uni-mt-n11 {
  margin-top: -22px;
}
.uni-mr-11 {
  margin-right: 22px;
}
.uni-mr-n11 {
  margin-right: -22px;
}
.uni-mb-11 {
  margin-bottom: 22px;
}
.uni-mb-n11 {
  margin-bottom: -22px;
}
.uni-ml-11 {
  margin-left: 22px;
}
.uni-ml-n11 {
  margin-left: -22px;
}
.uni-mx-11 {
  margin-left: 22px;
  margin-right: 22px;
}
.uni-mx-n11 {
  margin-left: -22px;
  margin-right: -22px;
}
.uni-my-11 {
  margin-top: 22px;
  margin-bottom: 22px;
}
.uni-my-n11 {
  margin-top: -22px;
  margin-bottom: -22px;
}
.uni-ma-11 {
  margin: 22px;
}
.uni-ma-n11 {
  margin: -22px;
}
.uni-mt-12 {
  margin-top: 24px;
}
.uni-mt-n12 {
  margin-top: -24px;
}
.uni-mr-12 {
  margin-right: 24px;
}
.uni-mr-n12 {
  margin-right: -24px;
}
.uni-mb-12 {
  margin-bottom: 24px;
}
.uni-mb-n12 {
  margin-bottom: -24px;
}
.uni-ml-12 {
  margin-left: 24px;
}
.uni-ml-n12 {
  margin-left: -24px;
}
.uni-mx-12 {
  margin-left: 24px;
  margin-right: 24px;
}
.uni-mx-n12 {
  margin-left: -24px;
  margin-right: -24px;
}
.uni-my-12 {
  margin-top: 24px;
  margin-bottom: 24px;
}
.uni-my-n12 {
  margin-top: -24px;
  margin-bottom: -24px;
}
.uni-ma-12 {
  margin: 24px;
}
.uni-ma-n12 {
  margin: -24px;
}
.uni-mt-13 {
  margin-top: 26px;
}
.uni-mt-n13 {
  margin-top: -26px;
}
.uni-mr-13 {
  margin-right: 26px;
}
.uni-mr-n13 {
  margin-right: -26px;
}
.uni-mb-13 {
  margin-bottom: 26px;
}
.uni-mb-n13 {
  margin-bottom: -26px;
}
.uni-ml-13 {
  margin-left: 26px;
}
.uni-ml-n13 {
  margin-left: -26px;
}
.uni-mx-13 {
  margin-left: 26px;
  margin-right: 26px;
}
.uni-mx-n13 {
  margin-left: -26px;
  margin-right: -26px;
}
.uni-my-13 {
  margin-top: 26px;
  margin-bottom: 26px;
}
.uni-my-n13 {
  margin-top: -26px;
  margin-bottom: -26px;
}
.uni-ma-13 {
  margin: 26px;
}
.uni-ma-n13 {
  margin: -26px;
}
.uni-mt-14 {
  margin-top: 28px;
}
.uni-mt-n14 {
  margin-top: -28px;
}
.uni-mr-14 {
  margin-right: 28px;
}
.uni-mr-n14 {
  margin-right: -28px;
}
.uni-mb-14 {
  margin-bottom: 28px;
}
.uni-mb-n14 {
  margin-bottom: -28px;
}
.uni-ml-14 {
  margin-left: 28px;
}
.uni-ml-n14 {
  margin-left: -28px;
}
.uni-mx-14 {
  margin-left: 28px;
  margin-right: 28px;
}
.uni-mx-n14 {
  margin-left: -28px;
  margin-right: -28px;
}
.uni-my-14 {
  margin-top: 28px;
  margin-bottom: 28px;
}
.uni-my-n14 {
  margin-top: -28px;
  margin-bottom: -28px;
}
.uni-ma-14 {
  margin: 28px;
}
.uni-ma-n14 {
  margin: -28px;
}
.uni-mt-15 {
  margin-top: 30px;
}
.uni-mt-n15 {
  margin-top: -30px;
}
.uni-mr-15 {
  margin-right: 30px;
}
.uni-mr-n15 {
  margin-right: -30px;
}
.uni-mb-15 {
  margin-bottom: 30px;
}
.uni-mb-n15 {
  margin-bottom: -30px;
}
.uni-ml-15 {
  margin-left: 30px;
}
.uni-ml-n15 {
  margin-left: -30px;
}
.uni-mx-15 {
  margin-left: 30px;
  margin-right: 30px;
}
.uni-mx-n15 {
  margin-left: -30px;
  margin-right: -30px;
}
.uni-my-15 {
  margin-top: 30px;
  margin-bottom: 30px;
}
.uni-my-n15 {
  margin-top: -30px;
  margin-bottom: -30px;
}
.uni-ma-15 {
  margin: 30px;
}
.uni-ma-n15 {
  margin: -30px;
}
.uni-mt-16 {
  margin-top: 32px;
}
.uni-mt-n16 {
  margin-top: -32px;
}
.uni-mr-16 {
  margin-right: 32px;
}
.uni-mr-n16 {
  margin-right: -32px;
}
.uni-mb-16 {
  margin-bottom: 32px;
}
.uni-mb-n16 {
  margin-bottom: -32px;
}
.uni-ml-16 {
  margin-left: 32px;
}
.uni-ml-n16 {
  margin-left: -32px;
}
.uni-mx-16 {
  margin-left: 32px;
  margin-right: 32px;
}
.uni-mx-n16 {
  margin-left: -32px;
  margin-right: -32px;
}
.uni-my-16 {
  margin-top: 32px;
  margin-bottom: 32px;
}
.uni-my-n16 {
  margin-top: -32px;
  margin-bottom: -32px;
}
.uni-ma-16 {
  margin: 32px;
}
.uni-ma-n16 {
  margin: -32px;
}
.uni-pt-0 {
  padding-top: 0px;
}
.uni-pt-n0 {
  padding-top: 0px;
}
.uni-pr-0 {
  padding-right: 0px;
}
.uni-pr-n0 {
  padding-right: 0px;
}
.uni-pb-0 {
  padding-bottom: 0px;
}
.uni-pb-n0 {
  padding-bottom: 0px;
}
.uni-pl-0 {
  padding-left: 0px;
}
.uni-pl-n0 {
  padding-left: 0px;
}
.uni-px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.uni-px-n0 {
  padding-left: 0px;
  padding-right: 0px;
}
.uni-py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.uni-py-n0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.uni-pa-0 {
  padding: 0px;
}
.uni-pa-n0 {
  padding: 0px;
}
.uni-pt-1 {
  padding-top: 2px;
}
.uni-pt-n1 {
  padding-top: -2px;
}
.uni-pr-1 {
  padding-right: 2px;
}
.uni-pr-n1 {
  padding-right: -2px;
}
.uni-pb-1 {
  padding-bottom: 2px;
}
.uni-pb-n1 {
  padding-bottom: -2px;
}
.uni-pl-1 {
  padding-left: 2px;
}
.uni-pl-n1 {
  padding-left: -2px;
}
.uni-px-1 {
  padding-left: 2px;
  padding-right: 2px;
}
.uni-px-n1 {
  padding-left: -2px;
  padding-right: -2px;
}
.uni-py-1 {
  padding-top: 2px;
  padding-bottom: 2px;
}
.uni-py-n1 {
  padding-top: -2px;
  padding-bottom: -2px;
}
.uni-pa-1 {
  padding: 2px;
}
.uni-pa-n1 {
  padding: -2px;
}
.uni-pt-2 {
  padding-top: 4px;
}
.uni-pt-n2 {
  padding-top: -4px;
}
.uni-pr-2 {
  padding-right: 4px;
}
.uni-pr-n2 {
  padding-right: -4px;
}
.uni-pb-2 {
  padding-bottom: 4px;
}
.uni-pb-n2 {
  padding-bottom: -4px;
}
.uni-pl-2 {
  padding-left: 4px;
}
.uni-pl-n2 {
  padding-left: -4px;
}
.uni-px-2 {
  padding-left: 4px;
  padding-right: 4px;
}
.uni-px-n2 {
  padding-left: -4px;
  padding-right: -4px;
}
.uni-py-2 {
  padding-top: 4px;
  padding-bottom: 4px;
}
.uni-py-n2 {
  padding-top: -4px;
  padding-bottom: -4px;
}
.uni-pa-2 {
  padding: 4px;
}
.uni-pa-n2 {
  padding: -4px;
}
.uni-pt-3 {
  padding-top: 6px;
}
.uni-pt-n3 {
  padding-top: -6px;
}
.uni-pr-3 {
  padding-right: 6px;
}
.uni-pr-n3 {
  padding-right: -6px;
}
.uni-pb-3 {
  padding-bottom: 6px;
}
.uni-pb-n3 {
  padding-bottom: -6px;
}
.uni-pl-3 {
  padding-left: 6px;
}
.uni-pl-n3 {
  padding-left: -6px;
}
.uni-px-3 {
  padding-left: 6px;
  padding-right: 6px;
}
.uni-px-n3 {
  padding-left: -6px;
  padding-right: -6px;
}
.uni-py-3 {
  padding-top: 6px;
  padding-bottom: 6px;
}
.uni-py-n3 {
  padding-top: -6px;
  padding-bottom: -6px;
}
.uni-pa-3 {
  padding: 6px;
}
.uni-pa-n3 {
  padding: -6px;
}
.uni-pt-4 {
  padding-top: 8px;
}
.uni-pt-n4 {
  padding-top: -8px;
}
.uni-pr-4 {
  padding-right: 8px;
}
.uni-pr-n4 {
  padding-right: -8px;
}
.uni-pb-4 {
  padding-bottom: 8px;
}
.uni-pb-n4 {
  padding-bottom: -8px;
}
.uni-pl-4 {
  padding-left: 8px;
}
.uni-pl-n4 {
  padding-left: -8px;
}
.uni-px-4 {
  padding-left: 8px;
  padding-right: 8px;
}
.uni-px-n4 {
  padding-left: -8px;
  padding-right: -8px;
}
.uni-py-4 {
  padding-top: 8px;
  padding-bottom: 8px;
}
.uni-py-n4 {
  padding-top: -8px;
  padding-bottom: -8px;
}
.uni-pa-4 {
  padding: 8px;
}
.uni-pa-n4 {
  padding: -8px;
}
.uni-pt-5 {
  padding-top: 10px;
}
.uni-pt-n5 {
  padding-top: -10px;
}
.uni-pr-5 {
  padding-right: 10px;
}
.uni-pr-n5 {
  padding-right: -10px;
}
.uni-pb-5 {
  padding-bottom: 10px;
}
.uni-pb-n5 {
  padding-bottom: -10px;
}
.uni-pl-5 {
  padding-left: 10px;
}
.uni-pl-n5 {
  padding-left: -10px;
}
.uni-px-5 {
  padding-left: 10px;
  padding-right: 10px;
}
.uni-px-n5 {
  padding-left: -10px;
  padding-right: -10px;
}
.uni-py-5 {
  padding-top: 10px;
  padding-bottom: 10px;
}
.uni-py-n5 {
  padding-top: -10px;
  padding-bottom: -10px;
}
.uni-pa-5 {
  padding: 10px;
}
.uni-pa-n5 {
  padding: -10px;
}
.uni-pt-6 {
  padding-top: 12px;
}
.uni-pt-n6 {
  padding-top: -12px;
}
.uni-pr-6 {
  padding-right: 12px;
}
.uni-pr-n6 {
  padding-right: -12px;
}
.uni-pb-6 {
  padding-bottom: 12px;
}
.uni-pb-n6 {
  padding-bottom: -12px;
}
.uni-pl-6 {
  padding-left: 12px;
}
.uni-pl-n6 {
  padding-left: -12px;
}
.uni-px-6 {
  padding-left: 12px;
  padding-right: 12px;
}
.uni-px-n6 {
  padding-left: -12px;
  padding-right: -12px;
}
.uni-py-6 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.uni-py-n6 {
  padding-top: -12px;
  padding-bottom: -12px;
}
.uni-pa-6 {
  padding: 12px;
}
.uni-pa-n6 {
  padding: -12px;
}
.uni-pt-7 {
  padding-top: 14px;
}
.uni-pt-n7 {
  padding-top: -14px;
}
.uni-pr-7 {
  padding-right: 14px;
}
.uni-pr-n7 {
  padding-right: -14px;
}
.uni-pb-7 {
  padding-bottom: 14px;
}
.uni-pb-n7 {
  padding-bottom: -14px;
}
.uni-pl-7 {
  padding-left: 14px;
}
.uni-pl-n7 {
  padding-left: -14px;
}
.uni-px-7 {
  padding-left: 14px;
  padding-right: 14px;
}
.uni-px-n7 {
  padding-left: -14px;
  padding-right: -14px;
}
.uni-py-7 {
  padding-top: 14px;
  padding-bottom: 14px;
}
.uni-py-n7 {
  padding-top: -14px;
  padding-bottom: -14px;
}
.uni-pa-7 {
  padding: 14px;
}
.uni-pa-n7 {
  padding: -14px;
}
.uni-pt-8 {
  padding-top: 16px;
}
.uni-pt-n8 {
  padding-top: -16px;
}
.uni-pr-8 {
  padding-right: 16px;
}
.uni-pr-n8 {
  padding-right: -16px;
}
.uni-pb-8 {
  padding-bottom: 16px;
}
.uni-pb-n8 {
  padding-bottom: -16px;
}
.uni-pl-8 {
  padding-left: 16px;
}
.uni-pl-n8 {
  padding-left: -16px;
}
.uni-px-8 {
  padding-left: 16px;
  padding-right: 16px;
}
.uni-px-n8 {
  padding-left: -16px;
  padding-right: -16px;
}
.uni-py-8 {
  padding-top: 16px;
  padding-bottom: 16px;
}
.uni-py-n8 {
  padding-top: -16px;
  padding-bottom: -16px;
}
.uni-pa-8 {
  padding: 16px;
}
.uni-pa-n8 {
  padding: -16px;
}
.uni-pt-9 {
  padding-top: 18px;
}
.uni-pt-n9 {
  padding-top: -18px;
}
.uni-pr-9 {
  padding-right: 18px;
}
.uni-pr-n9 {
  padding-right: -18px;
}
.uni-pb-9 {
  padding-bottom: 18px;
}
.uni-pb-n9 {
  padding-bottom: -18px;
}
.uni-pl-9 {
  padding-left: 18px;
}
.uni-pl-n9 {
  padding-left: -18px;
}
.uni-px-9 {
  padding-left: 18px;
  padding-right: 18px;
}
.uni-px-n9 {
  padding-left: -18px;
  padding-right: -18px;
}
.uni-py-9 {
  padding-top: 18px;
  padding-bottom: 18px;
}
.uni-py-n9 {
  padding-top: -18px;
  padding-bottom: -18px;
}
.uni-pa-9 {
  padding: 18px;
}
.uni-pa-n9 {
  padding: -18px;
}
.uni-pt-10 {
  padding-top: 20px;
}
.uni-pt-n10 {
  padding-top: -20px;
}
.uni-pr-10 {
  padding-right: 20px;
}
.uni-pr-n10 {
  padding-right: -20px;
}
.uni-pb-10 {
  padding-bottom: 20px;
}
.uni-pb-n10 {
  padding-bottom: -20px;
}
.uni-pl-10 {
  padding-left: 20px;
}
.uni-pl-n10 {
  padding-left: -20px;
}
.uni-px-10 {
  padding-left: 20px;
  padding-right: 20px;
}
.uni-px-n10 {
  padding-left: -20px;
  padding-right: -20px;
}
.uni-py-10 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.uni-py-n10 {
  padding-top: -20px;
  padding-bottom: -20px;
}
.uni-pa-10 {
  padding: 20px;
}
.uni-pa-n10 {
  padding: -20px;
}
.uni-pt-11 {
  padding-top: 22px;
}
.uni-pt-n11 {
  padding-top: -22px;
}
.uni-pr-11 {
  padding-right: 22px;
}
.uni-pr-n11 {
  padding-right: -22px;
}
.uni-pb-11 {
  padding-bottom: 22px;
}
.uni-pb-n11 {
  padding-bottom: -22px;
}
.uni-pl-11 {
  padding-left: 22px;
}
.uni-pl-n11 {
  padding-left: -22px;
}
.uni-px-11 {
  padding-left: 22px;
  padding-right: 22px;
}
.uni-px-n11 {
  padding-left: -22px;
  padding-right: -22px;
}
.uni-py-11 {
  padding-top: 22px;
  padding-bottom: 22px;
}
.uni-py-n11 {
  padding-top: -22px;
  padding-bottom: -22px;
}
.uni-pa-11 {
  padding: 22px;
}
.uni-pa-n11 {
  padding: -22px;
}
.uni-pt-12 {
  padding-top: 24px;
}
.uni-pt-n12 {
  padding-top: -24px;
}
.uni-pr-12 {
  padding-right: 24px;
}
.uni-pr-n12 {
  padding-right: -24px;
}
.uni-pb-12 {
  padding-bottom: 24px;
}
.uni-pb-n12 {
  padding-bottom: -24px;
}
.uni-pl-12 {
  padding-left: 24px;
}
.uni-pl-n12 {
  padding-left: -24px;
}
.uni-px-12 {
  padding-left: 24px;
  padding-right: 24px;
}
.uni-px-n12 {
  padding-left: -24px;
  padding-right: -24px;
}
.uni-py-12 {
  padding-top: 24px;
  padding-bottom: 24px;
}
.uni-py-n12 {
  padding-top: -24px;
  padding-bottom: -24px;
}
.uni-pa-12 {
  padding: 24px;
}
.uni-pa-n12 {
  padding: -24px;
}
.uni-pt-13 {
  padding-top: 26px;
}
.uni-pt-n13 {
  padding-top: -26px;
}
.uni-pr-13 {
  padding-right: 26px;
}
.uni-pr-n13 {
  padding-right: -26px;
}
.uni-pb-13 {
  padding-bottom: 26px;
}
.uni-pb-n13 {
  padding-bottom: -26px;
}
.uni-pl-13 {
  padding-left: 26px;
}
.uni-pl-n13 {
  padding-left: -26px;
}
.uni-px-13 {
  padding-left: 26px;
  padding-right: 26px;
}
.uni-px-n13 {
  padding-left: -26px;
  padding-right: -26px;
}
.uni-py-13 {
  padding-top: 26px;
  padding-bottom: 26px;
}
.uni-py-n13 {
  padding-top: -26px;
  padding-bottom: -26px;
}
.uni-pa-13 {
  padding: 26px;
}
.uni-pa-n13 {
  padding: -26px;
}
.uni-pt-14 {
  padding-top: 28px;
}
.uni-pt-n14 {
  padding-top: -28px;
}
.uni-pr-14 {
  padding-right: 28px;
}
.uni-pr-n14 {
  padding-right: -28px;
}
.uni-pb-14 {
  padding-bottom: 28px;
}
.uni-pb-n14 {
  padding-bottom: -28px;
}
.uni-pl-14 {
  padding-left: 28px;
}
.uni-pl-n14 {
  padding-left: -28px;
}
.uni-px-14 {
  padding-left: 28px;
  padding-right: 28px;
}
.uni-px-n14 {
  padding-left: -28px;
  padding-right: -28px;
}
.uni-py-14 {
  padding-top: 28px;
  padding-bottom: 28px;
}
.uni-py-n14 {
  padding-top: -28px;
  padding-bottom: -28px;
}
.uni-pa-14 {
  padding: 28px;
}
.uni-pa-n14 {
  padding: -28px;
}
.uni-pt-15 {
  padding-top: 30px;
}
.uni-pt-n15 {
  padding-top: -30px;
}
.uni-pr-15 {
  padding-right: 30px;
}
.uni-pr-n15 {
  padding-right: -30px;
}
.uni-pb-15 {
  padding-bottom: 30px;
}
.uni-pb-n15 {
  padding-bottom: -30px;
}
.uni-pl-15 {
  padding-left: 30px;
}
.uni-pl-n15 {
  padding-left: -30px;
}
.uni-px-15 {
  padding-left: 30px;
  padding-right: 30px;
}
.uni-px-n15 {
  padding-left: -30px;
  padding-right: -30px;
}
.uni-py-15 {
  padding-top: 30px;
  padding-bottom: 30px;
}
.uni-py-n15 {
  padding-top: -30px;
  padding-bottom: -30px;
}
.uni-pa-15 {
  padding: 30px;
}
.uni-pa-n15 {
  padding: -30px;
}
.uni-pt-16 {
  padding-top: 32px;
}
.uni-pt-n16 {
  padding-top: -32px;
}
.uni-pr-16 {
  padding-right: 32px;
}
.uni-pr-n16 {
  padding-right: -32px;
}
.uni-pb-16 {
  padding-bottom: 32px;
}
.uni-pb-n16 {
  padding-bottom: -32px;
}
.uni-pl-16 {
  padding-left: 32px;
}
.uni-pl-n16 {
  padding-left: -32px;
}
.uni-px-16 {
  padding-left: 32px;
  padding-right: 32px;
}
.uni-px-n16 {
  padding-left: -32px;
  padding-right: -32px;
}
.uni-py-16 {
  padding-top: 32px;
  padding-bottom: 32px;
}
.uni-py-n16 {
  padding-top: -32px;
  padding-bottom: -32px;
}
.uni-pa-16 {
  padding: 32px;
}
.uni-pa-n16 {
  padding: -32px;
}
.uni-radius-0 {
  border-radius: 0;
}
.uni-radius {
  border-radius: 5px;
}
.uni-radius-lg {
  border-radius: 10px;
}
.uni-radius-xl {
  border-radius: 30px;
}
.uni-radius-pill {
  border-radius: 9999px;
}
.uni-radius-circle {
  border-radius: 50%;
}
.uni-radius-t-0 {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.uni-radius-t {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.uni-radius-t-lg {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.uni-radius-t-xl {
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
}
.uni-radius-t-pill {
  border-top-left-radius: 9999px;
  border-top-right-radius: 9999px;
}
.uni-radius-t-circle {
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
}
.uni-radius-r-0 {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.uni-radius-r {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.uni-radius-r-lg {
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
}
.uni-radius-r-xl {
  border-top-right-radius: 30px;
  border-bottom-right-radius: 30px;
}
.uni-radius-r-pill {
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}
.uni-radius-r-circle {
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
}
.uni-radius-b-0 {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.uni-radius-b {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.uni-radius-b-lg {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.uni-radius-b-xl {
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
}
.uni-radius-b-pill {
  border-bottom-left-radius: 9999px;
  border-bottom-right-radius: 9999px;
}
.uni-radius-b-circle {
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
}
.uni-radius-l-0 {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.uni-radius-l {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.uni-radius-l-lg {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
}
.uni-radius-l-xl {
  border-top-left-radius: 30px;
  border-bottom-left-radius: 30px;
}
.uni-radius-l-pill {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}
.uni-radius-l-circle {
  border-top-left-radius: 50%;
  border-bottom-left-radius: 50%;
}
.uni-radius-tl-0 {
  border-top-left-radius: 0;
}
.uni-radius-tl {
  border-top-left-radius: 5px;
}
.uni-radius-tl-lg {
  border-top-left-radius: 10px;
}
.uni-radius-tl-xl {
  border-top-left-radius: 30px;
}
.uni-radius-tl-pill {
  border-top-left-radius: 9999px;
}
.uni-radius-tl-circle {
  border-top-left-radius: 50%;
}
.uni-radius-tr-0 {
  border-top-right-radius: 0;
}
.uni-radius-tr {
  border-top-right-radius: 5px;
}
.uni-radius-tr-lg {
  border-top-right-radius: 10px;
}
.uni-radius-tr-xl {
  border-top-right-radius: 30px;
}
.uni-radius-tr-pill {
  border-top-right-radius: 9999px;
}
.uni-radius-tr-circle {
  border-top-right-radius: 50%;
}
.uni-radius-br-0 {
  border-bottom-right-radius: 0;
}
.uni-radius-br {
  border-bottom-right-radius: 5px;
}
.uni-radius-br-lg {
  border-bottom-right-radius: 10px;
}
.uni-radius-br-xl {
  border-bottom-right-radius: 30px;
}
.uni-radius-br-pill {
  border-bottom-right-radius: 9999px;
}
.uni-radius-br-circle {
  border-bottom-right-radius: 50%;
}
.uni-radius-bl-0 {
  border-bottom-left-radius: 0;
}
.uni-radius-bl {
  border-bottom-left-radius: 5px;
}
.uni-radius-bl-lg {
  border-bottom-left-radius: 10px;
}
.uni-radius-bl-xl {
  border-bottom-left-radius: 30px;
}
.uni-radius-bl-pill {
  border-bottom-left-radius: 9999px;
}
.uni-radius-bl-circle {
  border-bottom-left-radius: 50%;
}
.uni-h1 {
  font-size: 32px;
  font-weight: 300;
  line-height: 50px;
}
.uni-h2 {
  font-size: 28px;
  font-weight: 300;
  line-height: 40px;
}
.uni-h3 {
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
}
.uni-h4 {
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
}
.uni-h5 {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}
.uni-h6 {
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
}
.uni-subtitle {
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}
.uni-body {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}
.uni-caption {
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}
.uni-btn {
  margin: 5px;
  color: #393939;
  border: 1px solid #ccc;
  font-size: 16px;
  font-weight: 200;
  background-color: #F9F9F9;
  overflow: visible;
}
.uni-btn::after {
  border: none;
}
.uni-btn:not([type]), .uni-btn[type=default] {
  color: #999;
}
.uni-btn:not([type])[loading], .uni-btn[type=default][loading] {
  background: none;
}
.uni-btn:not([type])[loading]::before, .uni-btn[type=default][loading]::before {
  margin-right: 5px;
}
.uni-btn:not([type])[disabled], .uni-btn[type=default][disabled] {
  color: #d6d6d6;
}
.uni-btn:not([type])[disabled], .uni-btn:not([type])[disabled][loading], .uni-btn:not([type])[disabled]:active, .uni-btn[type=default][disabled], .uni-btn[type=default][disabled][loading], .uni-btn[type=default][disabled]:active {
  color: #d6d6d6;
  background-color: #fafafa;
  border-color: #f0f0f0;
}
.uni-btn:not([type])[plain], .uni-btn[type=default][plain] {
  color: #999;
  background: none;
  border-color: #F0F0F0;
}
.uni-btn:not([type])[plain]:not([hover-class]):active, .uni-btn[type=default][plain]:not([hover-class]):active {
  background: none;
  color: #cccccc;
  border-color: #e6e6e6;
  outline: none;
}
.uni-btn:not([type])[plain][disabled], .uni-btn:not([type])[plain][disabled][loading], .uni-btn:not([type])[plain][disabled]:active, .uni-btn[type=default][plain][disabled], .uni-btn[type=default][plain][disabled][loading], .uni-btn[type=default][plain][disabled]:active {
  background: none;
  color: #d6d6d6;
  border-color: #f0f0f0;
}
.uni-btn:not([hover-class]):active {
  color: gray;
}
.uni-btn[size=mini] {
  font-size: 16px;
  font-weight: 200;
  border-radius: 8px;
}
.uni-btn.uni-btn-small {
  font-size: 14px;
}
.uni-btn.uni-btn-mini {
  font-size: 12px;
}
.uni-btn.uni-btn-radius {
  border-radius: 999px;
}
.uni-btn[type=primary] {
  color: #fff;
  background-color: #2979ff;
  border-color: #266feb;
}
.uni-btn[type=primary]:not([hover-class]):active {
  background: #256de6;
  border-color: #2161cc;
  color: #fff;
  outline: none;
}
.uni-btn[type=primary][loading] {
  color: #fff;
  background-color: #2979ff;
  border-color: #266feb;
}
.uni-btn[type=primary][loading]:not([hover-class]):active {
  background: #256de6;
  border-color: #2161cc;
  color: #fff;
  outline: none;
}
.uni-btn[type=primary][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=primary][disabled], .uni-btn[type=primary][disabled][loading], .uni-btn[type=primary][disabled]:not([hover-class]):active {
  color: #fff;
  border-color: #80aefa;
  background-color: #94bcff;
}
.uni-btn[type=primary][plain] {
  color: #2979ff;
  background-color: #eaf2ff;
  border-color: #bfd7ff;
}
.uni-btn[type=primary][plain]:not([hover-class]):active {
  background: #d4e4ff;
  color: #2979ff;
  outline: none;
  border-color: #94bcff;
}
.uni-btn[type=primary][plain][loading] {
  color: #2979ff;
  background-color: #eaf2ff;
  border-color: #bfd7ff;
}
.uni-btn[type=primary][plain][loading]:not([hover-class]):active {
  background: #d4e4ff;
  color: #2979ff;
  outline: none;
  border-color: #94bcff;
}
.uni-btn[type=primary][plain][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=primary][plain][disabled], .uni-btn[type=primary][plain][disabled]:active {
  color: #7fafff;
  background-color: #eaf2ff;
  border-color: #d4e4ff;
}
.uni-btn[type=success] {
  color: #fff;
  background-color: #18bc37;
  border-color: #16ad33;
}
.uni-btn[type=success]:not([hover-class]):active {
  background: #16a932;
  border-color: #13962c;
  color: #fff;
  outline: none;
}
.uni-btn[type=success][loading] {
  color: #fff;
  background-color: #18bc37;
  border-color: #16ad33;
}
.uni-btn[type=success][loading]:not([hover-class]):active {
  background: #16a932;
  border-color: #13962c;
  color: #fff;
  outline: none;
}
.uni-btn[type=success][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=success][disabled], .uni-btn[type=success][disabled][loading], .uni-btn[type=success][disabled]:not([hover-class]):active {
  color: #fff;
  border-color: #89c795;
  background-color: #8cde9b;
}
.uni-btn[type=success][plain] {
  color: #18bc37;
  background-color: #e8f8eb;
  border-color: #baebc3;
}
.uni-btn[type=success][plain]:not([hover-class]):active {
  background: #d1f2d7;
  color: #18bc37;
  outline: none;
  border-color: #8cde9b;
}
.uni-btn[type=success][plain][loading] {
  color: #18bc37;
  background-color: #e8f8eb;
  border-color: #baebc3;
}
.uni-btn[type=success][plain][loading]:not([hover-class]):active {
  background: #d1f2d7;
  color: #18bc37;
  outline: none;
  border-color: #8cde9b;
}
.uni-btn[type=success][plain][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=success][plain][disabled], .uni-btn[type=success][plain][disabled]:active {
  color: #74d787;
  background-color: #e8f8eb;
  border-color: #d1f2d7;
}
.uni-btn[type=error] {
  color: #fff;
  background-color: #e43d33;
  border-color: #d2382f;
}
.uni-btn[type=error]:not([hover-class]):active {
  background: #cd372e;
  border-color: #b63129;
  color: #fff;
  outline: none;
}
.uni-btn[type=error][loading] {
  color: #fff;
  background-color: #e43d33;
  border-color: #d2382f;
}
.uni-btn[type=error][loading]:not([hover-class]):active {
  background: #cd372e;
  border-color: #b63129;
  color: #fff;
  outline: none;
}
.uni-btn[type=error][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=error][disabled], .uni-btn[type=error][disabled][loading], .uni-btn[type=error][disabled]:not([hover-class]):active {
  color: #fff;
  border-color: #e4928d;
  background-color: #f29e99;
}
.uni-btn[type=error][plain] {
  color: #e43d33;
  background-color: #fceceb;
  border-color: #f7c5c2;
}
.uni-btn[type=error][plain]:not([hover-class]):active {
  background: #fad8d6;
  color: #e43d33;
  outline: none;
  border-color: #f29e99;
}
.uni-btn[type=error][plain][loading] {
  color: #e43d33;
  background-color: #fceceb;
  border-color: #f7c5c2;
}
.uni-btn[type=error][plain][loading]:not([hover-class]):active {
  background: #fad8d6;
  color: #e43d33;
  outline: none;
  border-color: #f29e99;
}
.uni-btn[type=error][plain][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=error][plain][disabled], .uni-btn[type=error][plain][disabled]:active {
  color: #ef8b85;
  background-color: #fceceb;
  border-color: #fad8d6;
}
.uni-btn[type=warning] {
  color: #fff;
  background-color: #f3a73f;
  border-color: #e09a3a;
}
.uni-btn[type=warning]:not([hover-class]):active {
  background: #db9639;
  border-color: #c28632;
  color: #fff;
  outline: none;
}
.uni-btn[type=warning][loading] {
  color: #fff;
  background-color: #f3a73f;
  border-color: #e09a3a;
}
.uni-btn[type=warning][loading]:not([hover-class]):active {
  background: #db9639;
  border-color: #c28632;
  color: #fff;
  outline: none;
}
.uni-btn[type=warning][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=warning][disabled], .uni-btn[type=warning][disabled][loading], .uni-btn[type=warning][disabled]:not([hover-class]):active {
  color: #fff;
  border-color: #f8c887;
  background-color: #f9d39f;
}
.uni-btn[type=warning][plain] {
  color: #f3a73f;
  background-color: #fef6ec;
  border-color: #fbe5c5;
}
.uni-btn[type=warning][plain]:not([hover-class]):active {
  background: #fdedd9;
  color: #f3a73f;
  outline: none;
  border-color: #f9d39f;
}
.uni-btn[type=warning][plain][loading] {
  color: #f3a73f;
  background-color: #fef6ec;
  border-color: #fbe5c5;
}
.uni-btn[type=warning][plain][loading]:not([hover-class]):active {
  background: #fdedd9;
  color: #f3a73f;
  outline: none;
  border-color: #f9d39f;
}
.uni-btn[type=warning][plain][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=warning][plain][disabled], .uni-btn[type=warning][plain][disabled]:active {
  color: #f8ca8c;
  background-color: #fef6ec;
  border-color: #fdedd9;
}
.uni-btn[type=info] {
  color: #fff;
  background-color: #8f939c;
  border-color: #848790;
}
.uni-btn[type=info]:not([hover-class]):active {
  background: #81848c;
  border-color: #72767d;
  color: #fff;
  outline: none;
}
.uni-btn[type=info][loading] {
  color: #fff;
  background-color: #8f939c;
  border-color: #848790;
}
.uni-btn[type=info][loading]:not([hover-class]):active {
  background: #81848c;
  border-color: #72767d;
  color: #fff;
  outline: none;
}
.uni-btn[type=info][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=info][disabled], .uni-btn[type=info][disabled][loading], .uni-btn[type=info][disabled]:not([hover-class]):active {
  color: #fff;
  border-color: #babcc2;
  background-color: #c7c9ce;
}
.uni-btn[type=info][plain] {
  color: #8f939c;
  background-color: #f4f4f5;
  border-color: #dddfe1;
}
.uni-btn[type=info][plain]:not([hover-class]):active {
  background: #e9e9eb;
  color: #8f939c;
  outline: none;
  border-color: #c7c9ce;
}
.uni-btn[type=info][plain][loading] {
  color: #8f939c;
  background-color: #f4f4f5;
  border-color: #dddfe1;
}
.uni-btn[type=info][plain][loading]:not([hover-class]):active {
  background: #e9e9eb;
  color: #8f939c;
  outline: none;
  border-color: #c7c9ce;
}
.uni-btn[type=info][plain][loading]::before {
  margin-right: 5px;
}
.uni-btn[type=info][plain][disabled], .uni-btn[type=info][plain][disabled]:active {
  color: #bcbec4;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}
body {
  background-color: #f5f5f5;
}
.example-info {
  font-size: 14px;
  color: #333;
  padding: 10px;
}