
.income-management-page[data-v-90b5ca22] {
	min-height: 100vh;
	background-color: #f5f7fa;
	color: #333;
	font-size: 0.875rem;
	line-height: 1.5;
	padding-bottom: 1.25rem;
}
.app-header[data-v-90b5ca22] {
	background: linear-gradient(135deg, #2e7d32, #2e7d32);
	color: white;
	padding: 1.25rem 0.9375rem;
	border-radius: 0 0 1.25rem 1.25rem;
	box-shadow: 0 0.25rem 0.75rem rgba(46, 125, 50, 0.2);
	margin-bottom: 1.25rem;
	position: relative;
	overflow: hidden;
}
.app-header[data-v-90b5ca22]::before {
	content: '';
	position: absolute;
	top: -3.125rem;
	right: -3.125rem;
	width: 12.5rem;
	height: 12.5rem;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}
.app-header[data-v-90b5ca22]::after {
	content: '';
	position: absolute;
	bottom: -1.875rem;
	left: -1.875rem;
	width: 6.25rem;
	height: 6.25rem;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}
.header-content[data-v-90b5ca22] {
	position: relative;
	z-index: 2;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.header-title[data-v-90b5ca22] {
	flex: 1;
}
.main-title[data-v-90b5ca22] {
	font-size: 1.375rem;
	font-weight: 600;
	margin-bottom: 0.3125rem;
	display: block;
}
.sub-title[data-v-90b5ca22] {
	font-size: 0.875rem;
	opacity: 0.9;
	display: block;
}
.user-avatar[data-v-90b5ca22] {
	width: 3.125rem;
	height: 3.125rem;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}
.avatar-icon[data-v-90b5ca22] {
	font-size: 1.5rem;
}
.date-selector[data-v-90b5ca22] {
	background: white;
	border-radius: 0.375rem;
	padding: 0.78125rem;
	box-shadow: 0 0.25rem 0.625rem rgba(0, 0, 0, 0.05);
	margin-bottom: 0.625rem;
}
.date-title[data-v-90b5ca22] {
	margin-bottom: 0.46875rem;
}
.date-label[data-v-90b5ca22] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
}
.date-controls[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.625rem;
	flex-wrap: wrap;
}
.date-input[data-v-90b5ca22] {
	padding: 0.625rem 0.9375rem;
	border: 0.0625rem solid #e0e0e0;
	border-radius: 0.625rem;
	font-size: 0.875rem;
	flex: 1;
	min-width: 0;
	background: #f9f9f9;
	text-align: center;
}
.date-separator[data-v-90b5ca22] {
	font-size: 0.875rem;
	color: #999;
}
.reset-button[data-v-90b5ca22] {
	align-self: center;
	background: #2e7d32;
	border-radius: 0.625rem;
	padding: 0.46875rem 0.78125rem;
	min-width: 2.5rem;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}
.reset-button[data-v-90b5ca22]:hover {
	background: #1b5e20;
	transform: translateY(-0.0625rem);
}
.reset-text[data-v-90b5ca22] {
	font-size: 0.75rem;
	color: white;
	font-weight: 500;
	text-align: center;
}

/* 调试刷新按钮样式 */
.debug-refresh-button[data-v-90b5ca22] {
	align-self: center;
	background: #1976d2;
	border-radius: 50%;
	padding: 0.46875rem;
	margin-left: 0.625rem;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
	width: 1.875rem;
	height: 1.875rem;
}
.debug-refresh-button[data-v-90b5ca22]:hover {
	background: #1565c0;
	transform: rotate(180deg);
}
.refresh-text[data-v-90b5ca22] {
	font-size: 1rem;
	color: white;
}
.refresh-button[data-v-90b5ca22] {
	align-self: center;
	background: #34c759;
	border-radius: 0.625rem;
	padding: 0.46875rem 0.78125rem;
	min-width: 2.5rem;
	margin-left: 0.3125rem;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}
.refresh-button[data-v-90b5ca22]:hover {
	background: #30d158;
	transform: translateY(-0.0625rem);
}

/* 占位符文本样式 */
.placeholder-text[data-v-90b5ca22] {
	color: #999;
	font-style: italic;
}
.btn[data-v-90b5ca22] {
	padding: 0.625rem 1.25rem;
	border: none;
	border-radius: 0.625rem;
	cursor: pointer;
	font-weight: 500;
	transition: all 0.3s ease;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	gap: 0.5rem;
	font-size: 0.875rem;
}
.btn-primary[data-v-90b5ca22] {
	background: #2e7d32;
	color: white;
}
.btn-outline[data-v-90b5ca22] {
	background: transparent;
	border: 0.0625rem solid #2e7d32;
	color: #2e7d32;
}
.btn-sm[data-v-90b5ca22] {
	padding: 0.5rem 0.75rem;
}
.cards-container[data-v-90b5ca22] {
	display: grid;
	grid-template-columns: 1fr;
	gap: 0.625rem;
	padding: 0 0.9375rem;
	margin-bottom: 0.9375rem;
}
.card[data-v-90b5ca22] {
	background: white;
	border-radius: 0.9375rem;
	padding: 0.9375rem;
	box-shadow: 0 0.25rem 0.625rem rgba(0, 0, 0, 0.05);
	border: 0.0625rem solid #f0f0f0;
	transition: all 0.3s ease;
}
.card[data-v-90b5ca22]:hover {
	transform: translateY(-0.125rem);
	box-shadow: 0 0.375rem 0.9375rem rgba(0, 0, 0, 0.1);
}
.profit-card[data-v-90b5ca22] {
	border-color: #2e7d32;
	background: linear-gradient(135deg, #f8fff8, #ffffff);
}
.card-header[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.46875rem;
	margin-bottom: 0.625rem;
	padding-bottom: 0.46875rem;
	border-bottom: 0.0625rem solid #f5f5f5;
}
.card-icon[data-v-90b5ca22] {
	font-size: 1.3125rem;
}
.card-title[data-v-90b5ca22] {
	font-size: 0.9375rem;
	font-weight: 600;
	color: #333;
}
.data-row[data-v-90b5ca22] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.375rem;
	padding: 0.3125rem 0;
}
.data-row[data-v-90b5ca22]:last-child {
	margin-bottom: 0;
}
.data-label[data-v-90b5ca22] {
	font-size: 0.8125rem;
	color: #666;
}
.data-value[data-v-90b5ca22] {
	font-size: 0.9375rem;
	font-weight: 600;
	color: #333;
}
.profit-value[data-v-90b5ca22] {
	font-size: 1.125rem;
}
.positive[data-v-90b5ca22] {
	color: #2e7d32 !important;
	font-weight: 600 !important;
}
.negative[data-v-90b5ca22] {
	color: #d32f2f !important;
	font-weight: 600 !important;
}










/* 收入详情管理区域样式 - 现代化设计风格 */

/* 收入详情管理主容器 */
.income-details-management-section[data-v-90b5ca22] {
	margin: 0 0.625rem 0.9375rem;
}
.income-section-header[data-v-90b5ca22] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.625rem;
	padding: 0 0.3125rem;
}
.section-title-group[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.section-icon[data-v-90b5ca22] {
	font-size: 1rem;
}
.section-title-text[data-v-90b5ca22] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
}
.records-count-badge[data-v-90b5ca22] {
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	padding: 0.25rem 0.5rem;
	border-radius: 0.625rem;
	box-shadow: 0 0.125rem 0.375rem rgba(46, 125, 50, 0.3);
}
.count-display-text[data-v-90b5ca22] {
	font-size: 0.75rem;
	color: white;
	font-weight: 500;
}

/* 数据加载状态显示样式 */
.data-loading-state[data-v-90b5ca22] {
	margin: 1.875rem 0;
}
.loading-status-card[data-v-90b5ca22] {
	background: white;
	border-radius: 0.625rem;
	padding: 2.5rem 1.25rem;
	text-align: center;
	box-shadow: 0 0.375rem 1.5rem rgba(0, 0, 0, 0.1);
	border: 0.03125rem solid rgba(255, 255, 255, 0.8);
}
.loading-status-card .loading-status-icon[data-v-90b5ca22] {
	font-size: 3.75rem;
	margin-bottom: 0.9375rem;
	opacity: 0.6;
}
.loading-status-card .loading-status-title[data-v-90b5ca22] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.loading-status-card .loading-status-description[data-v-90b5ca22] {
	font-size: 0.875rem;
	color: #666;
	line-height: 1.5;
}

/* 空数据状态显示样式 */
.data-empty-state[data-v-90b5ca22] {
	margin: 1.875rem 0;
}
.empty-status-card[data-v-90b5ca22] {
	background: white;
	border-radius: 0.625rem;
	padding: 2.5rem 1.25rem;
	text-align: center;
	box-shadow: 0 0.375rem 1.5rem rgba(0, 0, 0, 0.1);
	border: 0.03125rem solid rgba(255, 255, 255, 0.8);
}
.empty-status-card .empty-status-icon[data-v-90b5ca22] {
	font-size: 3.75rem;
	margin-bottom: 0.9375rem;
	opacity: 0.6;
}
.empty-status-card .empty-status-title[data-v-90b5ca22] {
	font-size: 1.125rem;
	font-weight: 600;
	color: #333;
	margin-bottom: 0.625rem;
}
.empty-status-card .empty-status-description[data-v-90b5ca22] {
	font-size: 0.875rem;
	color: #666;
	line-height: 1.5;
}

/* 收入记录列表容器 */
.income-records-container[data-v-90b5ca22] {
	display: flex;
	flex-direction: column;
	gap: 0.625rem;
}
.income-record-item[data-v-90b5ca22] {
	cursor: pointer;
	transition: all 0.3s ease;
}
.income-record-item[data-v-90b5ca22]:active {
	transform: translateY(0.0625rem);
}
.record-display-card[data-v-90b5ca22] {
	background: white;
	border-radius: 0.625rem;
	padding: 0.9375rem;
	box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);
	border: 0.03125rem solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
	overflow: hidden;
	position: relative;
}
.income-record-item:active .record-display-card[data-v-90b5ca22] {
	box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* 不同盈利状态的记录卡片样式 */
.income-record-item[data-profit-status="profitable"] .record-display-card[data-v-90b5ca22] {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(46, 125, 50, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(46, 125, 50, 0.03) 0%, transparent 50%);
	border-left: 0.1875rem solid #2e7d32;
	box-shadow: 0 0.25rem 1rem rgba(46, 125, 50, 0.15);
}
.income-record-item[data-profit-status="profitable"] .record-display-card[data-v-90b5ca22]::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 2.5rem;
	height: 2.5rem;
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(46, 125, 50, 0.2) 100%);
	border-radius: 0 0.625rem 0 2.5rem;
}
.income-record-item[data-profit-status="loss"] .record-display-card[data-v-90b5ca22] {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(211, 47, 47, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(211, 47, 47, 0.03) 0%, transparent 50%);
	border-left: 0.1875rem solid #d32f2f;
	box-shadow: 0 0.25rem 1rem rgba(211, 47, 47, 0.15);
}
.income-record-item[data-profit-status="loss"] .record-display-card[data-v-90b5ca22]::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 2.5rem;
	height: 2.5rem;
	background: linear-gradient(135deg, rgba(211, 47, 47, 0.1) 0%, rgba(211, 47, 47, 0.2) 100%);
	border-radius: 0 0.625rem 0 2.5rem;
}
.income-record-item[data-profit-status="placeholder"] .record-display-card[data-v-90b5ca22] {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(117, 117, 117, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(117, 117, 117, 0.03) 0%, transparent 50%);
	border-left: 0.1875rem solid #757575;
	box-shadow: 0 0.25rem 1rem rgba(117, 117, 117, 0.15);
}
.income-record-item[data-profit-status="placeholder"] .record-display-card[data-v-90b5ca22]::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 2.5rem;
	height: 2.5rem;
	background: linear-gradient(135deg, rgba(117, 117, 117, 0.1) 0%, rgba(117, 117, 117, 0.2) 100%);
	border-radius: 0 0.625rem 0 2.5rem;
}

/* 记录卡片点击交互效果 */
.income-record-item[data-profit-status="profitable"]:active .record-display-card[data-v-90b5ca22] {
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%, rgba(46, 125, 50, 0.12) 100%);
	box-shadow: 0 0.125rem 0.5rem rgba(46, 125, 50, 0.25);
}
.income-record-item[data-profit-status="loss"]:active .record-display-card[data-v-90b5ca22] {
	background: linear-gradient(135deg, rgba(211, 47, 47, 0.08) 0%, rgba(211, 47, 47, 0.12) 100%);
	box-shadow: 0 0.125rem 0.5rem rgba(211, 47, 47, 0.25);
}
.income-record-item[data-profit-status="placeholder"]:active .record-display-card[data-v-90b5ca22] {
	background: linear-gradient(135deg, rgba(117, 117, 117, 0.08) 0%, rgba(117, 117, 117, 0.12) 100%);
	box-shadow: 0 0.125rem 0.5rem rgba(117, 117, 117, 0.25);
}

/* 记录卡片头部区域 */
.record-card-header[data-v-90b5ca22] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 0.78125rem;
	position: relative;
	z-index: 1;
}
.record-date-display-section[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.date-display-icon[data-v-90b5ca22] {
	font-size: 0.875rem;
	color: #666;
}
.date-display-info[data-v-90b5ca22] {
	display: flex;
	flex-direction: column;
	gap: 0.125rem;
}
.date-display-text[data-v-90b5ca22] {
	font-size: 1rem;
	font-weight: 600;
	color: #333;
	line-height: 1.2;
}
.weekday-display-text[data-v-90b5ca22] {
	font-size: 0.75rem;
	color: #666;
	line-height: 1.2;
}
.profit-status-indicator-badge[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.25rem;
	padding: 0.375rem 0.625rem;
	border-radius: 0.9375rem;
	font-size: 0.8125rem;
	font-weight: 600;
	box-shadow: 0 0.125rem 0.375rem rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 1;
}
.profit-status-indicator-badge.profitable[data-v-90b5ca22] {
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	color: white;
	box-shadow: 0 0.125rem 0.375rem rgba(46, 125, 50, 0.3);
}
.profit-status-indicator-badge.loss[data-v-90b5ca22] {
	background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
	color: white;
	box-shadow: 0 0.125rem 0.375rem rgba(211, 47, 47, 0.3);
}
.profit-status-indicator-badge.placeholder[data-v-90b5ca22] {
	background: linear-gradient(135deg, #757575 0%, #9e9e9e 100%);
	color: white;
	box-shadow: 0 0.125rem 0.375rem rgba(117, 117, 117, 0.3);
}
.profit-status-indicator-badge.neutral[data-v-90b5ca22] {
	background: linear-gradient(135deg, #616161 0%, #757575 100%);
	color: white;
	box-shadow: 0 0.125rem 0.375rem rgba(97, 97, 97, 0.3);
}
.status-indicator-icon[data-v-90b5ca22] {
	font-size: 0.75rem;
}
.status-indicator-text[data-v-90b5ca22] {
	font-size: 0.8125rem;
	font-weight: 600;
}

/* 记录内容区域 */
.record-content-area[data-v-90b5ca22] {
	margin-bottom: 0.78125rem;
	position: relative;
	z-index: 1;
}

/* 基础业务信息摘要 */
.basic-business-info-summary[data-v-90b5ca22] {
	margin-bottom: 0.625rem;
}
.business-info-grid[data-v-90b5ca22] {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.46875rem;
	margin-bottom: 0.46875rem;
}
.business-info-card[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
	padding: 0.625rem;
	border-radius: 0.375rem;
	border: 0.03125rem solid rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(233, 236, 239, 0.8) 100%);
}
.primary-financial-card[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
	padding: 0.625rem;
	border-radius: 0.375rem;
	border: 0.03125rem solid rgba(46, 125, 50, 0.2);
	transition: all 0.3s ease;
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%, rgba(46, 125, 50, 0.12) 100%);
}
.business-info-icon[data-v-90b5ca22] {
	font-size: 0.875rem;
	color: #666;
	opacity: 0.8;
}
.business-info-details[data-v-90b5ca22] {
	display: flex;
	flex-direction: column;
	gap: 0.125rem;
	flex: 1;
}
.business-info-label[data-v-90b5ca22] {
	font-size: 0.9375rem;
	color: #666;
	line-height: 1.2;
}
.business-info-value[data-v-90b5ca22] {
	font-size: 0.875rem;
	color: #333;
	font-weight: 600;
	line-height: 1.2;
}
.primary-financial-icon[data-v-90b5ca22] {
	font-size: 0.875rem;
	color: #2e7d32;
}
.primary-financial-info[data-v-90b5ca22] {
	display: flex;
	flex-direction: column;
	gap: 0.125rem;
	flex: 1;
}
.primary-financial-label[data-v-90b5ca22] {
	font-size: 0.9375rem;
	color: #666;
	line-height: 1.2;
}
.primary-financial-value[data-v-90b5ca22] {
	font-size: 0.875rem;
	font-weight: 600;
	line-height: 1.2;
}
.primary-financial-value.income[data-v-90b5ca22] {
	color: #2e7d32;
}
.customer-information-display[data-v-90b5ca22] {
	text-align: center;
	padding: 0.25rem 0.5rem;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.9) 100%);
	border-radius: 0.625rem;
	border: 0.03125rem solid rgba(0, 0, 0, 0.05);
}
.customer-information-text[data-v-90b5ca22] {
	font-size: 0.9375rem;
	font-weight: 500;
}
.customer-information-text.positive[data-v-90b5ca22] {
	color: #2e7d32;
}
.customer-information-text.negative[data-v-90b5ca22] {
	color: #d32f2f;
}
.customer-information-text.neutral[data-v-90b5ca22] {
	color: #666;
}
.customer-information-text.placeholder[data-v-90b5ca22] {
	color: #999;
}

/* 财务数据详情摘要 */
.financial-data-details-summary[data-v-90b5ca22] {
	margin-bottom: 0.625rem;
}
.financial-data-grid[data-v-90b5ca22] {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 0.46875rem;
}

/* 记录摘要底部区域 */
.record-summary-footer[data-v-90b5ca22] {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.625rem 0.78125rem;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.9) 100%);
	border-radius: 0.5rem;
	border: 0.03125rem solid rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 1;
}
.profit-summary-display[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.375rem;
}
.profit-summary-label[data-v-90b5ca22] {
	font-size: 0.9375rem;
	color: #666;
	font-weight: 500;
}
.profit-summary-value[data-v-90b5ca22] {
	font-size: 1.125rem;
	font-weight: 700;
	text-shadow: 0 0.03125rem 0.0625rem rgba(0, 0, 0, 0.1);
}
.profit-summary-value.positive[data-v-90b5ca22] {
	color: #2e7d32;
}
.profit-summary-value.negative[data-v-90b5ca22] {
	color: #d32f2f;
}
.profit-summary-value.neutral[data-v-90b5ca22] {
	color: #666;
}
.profit-summary-value.placeholder[data-v-90b5ca22] {
	color: #999;
}
.detail-navigation-button[data-v-90b5ca22] {
	display: flex;
	align-items: center;
	gap: 0.25rem;
	padding: 0.375rem 0.625rem;
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	border-radius: 0.78125rem;
	box-shadow: 0 0.125rem 0.375rem rgba(46, 125, 50, 0.3);
}
.navigation-button-text[data-v-90b5ca22] {
	font-size: 0.75rem;
	color: white;
	font-weight: 500;
}
.navigation-button-icon[data-v-90b5ca22] {
	font-size: 0.625rem;
	color: white;
	font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.enhanced-income-section[data-v-90b5ca22] {
		margin-left: 0.46875rem;
		margin-right: 0.46875rem;
}
.income-card[data-v-90b5ca22] {
		padding: 0.78125rem;
}
.financial-grid[data-v-90b5ca22],
	.details-grid[data-v-90b5ca22] {
		grid-template-columns: 1fr;
		gap: 0.375rem;
}
.enhanced-income-footer[data-v-90b5ca22] {
		flex-direction: column;
		gap: 0.46875rem;
		align-items: stretch;
}
.view-detail-btn[data-v-90b5ca22] {
		justify-content: center;
}
.profit-status-badge[data-v-90b5ca22] {
		padding: 0.3125rem 0.5rem;
		font-size: 0.75rem;
}
.date-text[data-v-90b5ca22] {
		font-size: 0.9375rem;
}
.weekday-text[data-v-90b5ca22] {
		font-size: 0.6875rem;
}
.financial-value[data-v-90b5ca22] {
		font-size: 0.8125rem;
}
.summary-value[data-v-90b5ca22] {
		font-size: 1rem;
}
}

/* 大屏设备优化 */
@media (min-width: 1200rpx) {
.enhanced-income-list[data-v-90b5ca22] {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 0.625rem;
}
.financial-grid[data-v-90b5ca22],
	.details-grid[data-v-90b5ca22] {
		grid-template-columns: 1fr 1fr;
}
}
